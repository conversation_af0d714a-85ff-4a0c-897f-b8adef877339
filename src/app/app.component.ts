import { ChangeDetectionStrategy, Component, inject, OnD<PERSON>roy, OnInit, Renderer2 } from '@angular/core';
import { ChildActivationEnd, Data, NavigationEnd, Router, RouterOutlet } from '@angular/router';
import { SchemaOrgService, SchemaOrgWebpageDataTemplate, SeoService, UtilService } from '@trendency/kesma-core';
import { AnalyticsService, ScrollPositionService } from '@trendency/kesma-ui';
import { Observable, Subject } from 'rxjs';
import { buffer, filter, map, takeUntil, tap } from 'rxjs/operators';
import { defaultMetaInfo, RssFeedService, UrlService } from './shared';
import { environment } from '../environments/environment';

@Component({
  selector: 'app-root',
  template: '<router-outlet></router-outlet>',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [RouterOutlet],
})
export class AppComponent implements OnInit, OnD<PERSON>roy {
  private readonly renderer = inject(Renderer2);
  unsubscribe$: Subject<void> = new Subject<void>();
  private isFirstNavigation = true;

  constructor(
    private readonly seoService: SeoService,
    private readonly utilsService: UtilService,
    private readonly schemaService: SchemaOrgService,
    private readonly router: Router,
    private readonly analyticsService: AnalyticsService, //private readonly gtmService: GoogleTagManagerService,
    private readonly rssFeedService: RssFeedService,
    private readonly urlService: UrlService,
    private readonly scrollPositionService: ScrollPositionService
  ) {}

  ngOnInit(): void {
    this.seoService.setMetaData(defaultMetaInfo, { skipSeoMetaCheck: true });
    SchemaOrgWebpageDataTemplate.url = this.seoService.hostUrl;
    if (this.utilsService.isBrowser()) {
      this.setupAnalyticsTracking();
      this.scrollPositionService.setupScrollPositionListener();
    }

    (this.router.events.pipe(filter((event) => event instanceof NavigationEnd)) as Observable<NavigationEnd>)
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe((event: NavigationEnd): void => {
        this.schemaService.removeStructuredData();
        this.schemaService.insertSchema(SchemaOrgWebpageDataTemplate);

        // Columns' RSS Feed
        if (!this.isFirstNavigation) {
          this.rssFeedService.removeRssFeed(true);
        }
        this.rssFeedService.addRssFeed(true);
        this.urlService.setPreviousUrl(event.url);
      });
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }

  private setupAnalyticsTracking(): void {
    // Navigation end used to trigger gemius and gtag
    const navigationEnd$: Observable<NavigationEnd> = (this.router.events.pipe(filter((e) => e instanceof NavigationEnd)) as Observable<NavigationEnd>).pipe(
      tap((event: NavigationEnd) => {
        if (!this.isFirstNavigation && typeof pp_gemius_hit !== 'undefined') {
          pp_gemius_hit(environment.gemiusId, `page=${event.urlAfterRedirects}`);
        }
        // wrapped in a setTimeout because the router event fires before we can grab <title> from the page's <head>.
        setTimeout(() => {
          this.isFirstNavigation = false;
        }, 0);
      })
    );

    // Child activationEnd to get the leaf route data in order to see if we send pageViews there.
    (this.router.events.pipe(filter((e) => e instanceof ChildActivationEnd)) as Observable<ChildActivationEnd>)
      .pipe(
        // ChildActivationEnd triggers for every path activation, we need only the leaf so after navigation end
        // we get all of the childActivationEnd events and we only need the first one.
        buffer(navigationEnd$),
        map(([leafNode]: ChildActivationEnd[]) => leafNode?.snapshot?.firstChild?.data),
        map((data?: Data & { omitGlobalPageView?: boolean }) => data?.omitGlobalPageView),
        takeUntil(this.unsubscribe$)
      )
      .subscribe((shouldNotSendGlobalAnalytics?: boolean) => {
        if (shouldNotSendGlobalAnalytics) {
          return;
        }
        setTimeout(() => {
          this.analyticsService.sendPageView();
        }, 100);
      });
  }
}
