import { Pipe, PipeTransform } from '@angular/core';
import { capitalize } from '../constants';

@Pipe({
  name: 'titleWithPage',
})
export class TitleWithPagePipe implements PipeTransform {
  transform(value: string | undefined, page: number = 0, useCapitalize: boolean = false): string {
    let title = value ?? '';

    if (useCapitalize) {
      title = capitalize(title);
    }

    if (page <= 0) {
      return title;
    }

    return `${page + 1}. oldal - ${title}`;
  }
}
