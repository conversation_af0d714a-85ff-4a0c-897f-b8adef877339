import { Injectable } from '@angular/core';
import { ReqService } from '@trendency/kesma-core';
import { ApiResponseMeta, ApiResult, Article, ArticleCard, BackendArticle, BackendRecommendationsData, RecommendationsData } from '@trendency/kesma-ui';
import { Observable, of } from 'rxjs';
import { catchError, map, switchMap } from 'rxjs/operators';
import { ApiService } from './api.service';
import { backendArticlesToArticles, backendRecommendedArticleToArticleCard, externalRecommendationToArticleCard } from '../utils';

@Injectable({
  providedIn: 'root',
})
export class ArticleService {
  constructor(
    private readonly reqService: ReqService,
    private readonly apiService: ApiService
  ) {}

  getArticlePreview(articleSlug: string, previewHash: string, previewType: string): Observable<ApiResult<Article>> {
    const timestamp = Math.floor(new Date().getTime());
    return this.reqService
      .get<ApiResult<BackendArticle, ApiResponseMeta>>(
        `/content-page/article/${articleSlug}/preview/view?previewHash=${previewHash}&t=${timestamp.toString()}`,
        {
          params: {
            previewType,
          },
        }
      )
      .pipe(
        map(({ data, meta }) => ({
          data: backendArticlesToArticles(data),
          meta,
        }))
      );
  }

  getArticle(category: string, articleSlug: string): Observable<ApiResult<Article>> {
    return this.reqService.get<ApiResult<BackendArticle>>(`/content-page/article/${category}/${articleSlug}`).pipe(
      map(({ data, meta }: ApiResult<BackendArticle>) => ({
        data: backendArticlesToArticles(data),
        meta,
      }))
    );
  }

  getArticleRecommendations(articleSlug: string, categorySlug?: string): Observable<ApiResult<RecommendationsData>> {
    if (categorySlug) {
      return this.apiService.getCategoryArticles(categorySlug, 0, 5).pipe(
        switchMap((categoryArticles) => {
          return this.reqService.get<ApiResult<BackendRecommendationsData>>(`/content-page/article/${articleSlug}/recommendation`).pipe(
            catchError(() =>
              of({
                data: {},
                meta: {},
              } as any)
            ),
            map(({ data, meta }: ApiResult<BackendRecommendationsData>) => ({
              data: {
                ...data,
                categoryArticles: categoryArticles.data as ArticleCard[],
                highPriorityArticles: data.highPriorityArticles?.map(backendRecommendedArticleToArticleCard),
                lowPriorityArticles: data.lowPriorityArticles?.map(backendRecommendedArticleToArticleCard),
                externalRecommendation: data.externalRecommendation?.map(externalRecommendationToArticleCard),
                lastThreeDaysMostReadArticles: data.lastThreeDaysMostReadArticles?.map(backendRecommendedArticleToArticleCard),
                articlesByTags: data.articlesByTags?.map(backendRecommendedArticleToArticleCard),
              },
              meta,
            }))
          );
        })
      );
    } else {
      return this.reqService.get<ApiResult<BackendRecommendationsData>>(`/content-page/article/${articleSlug}/recommendation`).pipe(
        catchError(() =>
          of({
            data: {},
            meta: {},
          } as any)
        ),
        map(({ data, meta }: ApiResult<BackendRecommendationsData>) => ({
          data: {
            ...data,
            highPriorityArticles: data.highPriorityArticles?.map(backendRecommendedArticleToArticleCard),
            lowPriorityArticles: data.lowPriorityArticles?.map(backendRecommendedArticleToArticleCard),
            externalRecommendation: data.externalRecommendation?.map(externalRecommendationToArticleCard),
            lastThreeDaysMostReadArticles: data.lastThreeDaysMostReadArticles?.map(backendRecommendedArticleToArticleCard),
            articlesByTags: data.articlesByTags?.map(backendRecommendedArticleToArticleCard),
          },
          meta,
        }))
      );
    }
  }
}
