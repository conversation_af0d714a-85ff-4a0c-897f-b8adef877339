import { inject, Injectable } from '@angular/core';
import { LayoutApiData } from '@trendency/kesma-ui/lib/definitions/layout.definitions';
import { exhaustMap, forkJoin, Observable, of } from 'rxjs';
import { catchError, map, tap } from 'rxjs/operators';
import { format } from 'date-fns';
import { toZonedTime } from 'date-fns-tz';
import { GastroApiService } from '../../feature/gastro/shared/gastro-api.service';
import {
  LayoutElementContentConfigurationGastroBase,
  LayoutElementContentConfigurationGastroBaseSelectedOccasion,
} from '../../feature/gastro/definitions/experience-layout.definitions';

const autoFilterExperienceCategoriesProperty = 'filterExperienceCategories';
const autoFilterExperienceProperty = 'filterExperience';

type BasicItem = {
  id: string;
} & Record<string, any>;

@Injectable({
  providedIn: 'root',
})
export class GastroLayoutFillService {
  gastroApi = inject(GastroApiService);
  /**
   * The ids that should be excluded from the upcoming requests.
   * This is globally used for a given layout data filling.
   */
  excludeIds = new Set<string>();
  /**
   * The ids that are associated with the newest autofill.
   * These are only used for items that use the newest autofill.
   */
  newestExcludeIds = new Set<string>();

  /**
   * Fills the layout data with the autofill data.
   * First it check the explicitly filled items and requests them in parallel.
   * Next it check the items that should be autofilled and requests them sequentially.
   * It is necessary to always save the used ids, as we don't want to retrieve the same data multiple times.
   * That's why we can't do the autofilling in parallel, but we can do the explicit filling next to each other.
   * @param content the content of the layout
   * @param struct the structure of the layout
   */
  fillLayoutData({ content, struct }: LayoutApiData): Observable<LayoutApiData> {
    this.excludeIds = new Set<string>();
    this.newestExcludeIds = new Set<string>();
    const selectedOccasionsProperty = 'selectedOccasions';
    const layoutItemIdsForGasztro: Record<string, number> = {};
    content = [...content];
    // First we extract what layout items are needed for the gastro auto filling.
    content?.map((item, index) => {
      if (!(selectedOccasionsProperty in item)) {
        return;
      }
      layoutItemIdsForGasztro[item.layoutElementId] = index;
    });
    // If there are no items that need gastro autofilling, we can return the original data and skip the rest.
    if (Object.keys(layoutItemIdsForGasztro).length === 0) {
      return of({ content, struct });
    }

    let res$: Observable<any> = of([]);

    // First get the explicit items.
    const explicitFill = Object.keys(layoutItemIdsForGasztro)
      // Filter the items that have explicit filling.
      .filter((key) => {
        const configIndex = layoutItemIdsForGasztro[key];
        const config = content[configIndex] as LayoutElementContentConfigurationGastroBase;
        return config.selectedOccasions?.some((o) => o?.id);
      })
      // Map the items to the observables that will do the requests.
      .map((key) => {
        const configIndex = layoutItemIdsForGasztro[key];
        const config = content[configIndex] as LayoutElementContentConfigurationGastroBase;
        const selectedOccasions = config.selectedOccasions || [];
        const observables = selectedOccasions
          .map((o, index) =>
            o?.id
              ? this.gastroApi.getExperienceOccasionById(o.id).pipe(
                  tap((res) => {
                    if (!res) {
                      return;
                    }
                    const item = res.data?.[0];
                    if (!item) {
                      return;
                    }
                    this.excludeItem(config, item.id);
                    selectedOccasions[index].data = item;
                  })
                )
              : null
          )
          // Last we filter the observables that are not null to avoid undefined values.
          .filter((obs) => !!obs) as Observable<unknown>[];
        // Fork join the observables to get the data in parallel for the layout item.
        return forkJoin(observables);
      });

    // If there are any explicit filling items, we need to exhaust the observables to get the data.
    if (explicitFill.length > 0) {
      res$ = res$.pipe(exhaustMap(() => forkJoin(explicitFill)));
    }

    // Now we can get the autofill items.
    Object.keys(layoutItemIdsForGasztro).forEach((key) => {
      const configIndex = layoutItemIdsForGasztro[key];
      let config = content[configIndex] as LayoutElementContentConfigurationGastroBase;
      // First we get the consume count for the items. This is not the final value,
      // as we will fine-tune it later to also account for the explicit filling.
      let consumeCount = config?.selectedOccasions?.length || 0;
      res$ = res$.pipe(
        exhaustMap(() => {
          config = content[configIndex] as LayoutElementContentConfigurationGastroBase;
          return this.getAutoFilterFunctionForLayoutItem(config).pipe(
            tap((items) => {
              config = content[configIndex] as LayoutElementContentConfigurationGastroBase;
              // Get the exact number of items that should be consumed based on the explicit filling.
              const filledOccasionsCount = config?.selectedOccasions?.filter((item) => !!item?.data)?.length || 0;
              consumeCount = consumeCount - filledOccasionsCount || 0;
              const indexesToConsume = this.findIndexesToConsume(config, consumeCount);
              // Not we only need the number of items that should be added to the list.
              const itemsToAdd = this.getItems(items, consumeCount);
              itemsToAdd.forEach((item) => {
                // Also exclude the ids of these items from the upcoming requests.
                this.excludeItem(config, item.id);
              });
              const autofilledSelectedOccasions = this.fillAutofilledItems(config, itemsToAdd, indexesToConsume);
              // Add the data to the layout item.
              (content[configIndex] as any) = {
                ...content[configIndex],
                selectedOccasions: autofilledSelectedOccasions,
              };
            })
          );
        })
      );
    });

    return res$.pipe(map(() => ({ content, struct })));
  }

  /**
   * Checks what kind of autofill should be used and returns the corresponding observable.
   * @param config
   */
  getAutoFilterFunctionForLayoutItem(config: LayoutElementContentConfigurationGastroBase): Observable<BasicItem[]> {
    const autoFill = config?.autoFill;
    const filterExperienceCategories = autoFill?.[autoFilterExperienceCategoriesProperty];
    const filterExperience = autoFill?.[autoFilterExperienceProperty];
    let filterExperienceCategoryIds: string[] = [];
    if (filterExperience?.id) {
      return this.getAutoExperienceForLayoutItem(config);
    }

    if (Array.isArray(filterExperienceCategories)) {
      filterExperienceCategoryIds = filterExperienceCategories.map((category) => category?.id).filter((id) => !!id);
    } else {
      filterExperienceCategoryIds = filterExperienceCategories?.id ? [filterExperienceCategories?.id] : [];
    }

    if (filterExperienceCategoryIds?.length) {
      return this.getAutoCategoryForLayoutItem(filterExperienceCategoryIds, config);
    }
    return this.getAutoItem(config);
  }

  /**
   * Returns items for the given experience category id in the layout element config.
   * @param config
   */
  getAutoCategoryForLayoutItem(experienceCategoryIds: string[], config: LayoutElementContentConfigurationGastroBase): Observable<BasicItem[]> {
    return this.gastroApi.getExperienceOccasionsAutoByMultipleCategories(experienceCategoryIds, this.getCommonParams(config)).pipe(map((res) => res.data));
  }

  /**
   * Returns items for the given experience id in the layout element config.
   * @param config
   */
  getAutoExperienceForLayoutItem(config: LayoutElementContentConfigurationGastroBase): Observable<BasicItem[]> {
    const experienceId = config?.autoFill?.filterExperience?.id;
    if (!experienceId) {
      return of([]);
    }
    return this.gastroApi.getExperienceOccasionsAutoByExperience(experienceId, this.getCommonParams(config)).pipe(map((res) => res.data));
  }

  /**
   * Returns items without experience or experience category filtering.
   * @param config
   */
  getAutoItem(config: LayoutElementContentConfigurationGastroBase): Observable<BasicItem[]> {
    return this.gastroApi.getExperienceOccasionsAuto(this.getCommonParams(config)).pipe(
      map((res) => res.data),
      catchError(() => of([] as BasicItem[]))
    );
  }

  /**
   * Returns how many items should be consumed from the autofill list.
   * It is necessary to calculate as there could be items that are explicitly filled,
   * so we have to subtract them from all the items that should be shown in the component.
   * @param config
   */
  getConsumeCountForItem(config: LayoutElementContentConfigurationGastroBase): number {
    const occasionsCount = config?.selectedOccasions?.length || 0;
    const filledOccasionsCount = config?.selectedOccasions?.filter((item) => !!item?.data)?.length || 0;
    const consumeCount = occasionsCount - filledOccasionsCount || 0;
    return consumeCount;
  }

  /**
   * Return the common params for the request. This is used for all the auto api endpoints.
   * @param config
   */
  getCommonParams(config: LayoutElementContentConfigurationGastroBase): Record<string, string | string[]> {
    const consumeCount = this.getConsumeCountForItem(config);
    const notInIds = this.getExcludedIdsParam(config);
    const displayComingSoon = 0;
    const utcNow = format(toZonedTime(new Date(), 'UTC'), 'yyyy-MM-dd HH:mm:ss');

    const params = {
      'notInIds[]': notInIds,
      displayComingSoon: displayComingSoon ? '1' : '0',
      rowCount_limit: consumeCount.toString(),
      occasionDateGreater_filter: utcNow,
      ...(!config.remainingNumberOfSeatsOrder && { start_of_experience_event_order: ['asc'] }),
    };

    if (config.remainingNumberOfSeatsOrder) {
      return {
        ...params,
        'remaining_number_of_seats_order[]': 'asc',
        remainingNumberOfSeats_filter: '1',
      };
    }
    return params;
  }

  /**
   * Prepares the notInIds parameter for the request. When the newest autofill is enabled we only use the newest excludeIds.
   * Otherwise we use the excludeIds and the newest excludeIds.
   * @param config
   */
  getExcludedIdsParam(config: LayoutElementContentConfigurationGastroBase): string[] {
    const isNewestAutoFilled = this.isLayoutElementHasNewestAutoFilled(config);
    return isNewestAutoFilled ? Array.from(this.newestExcludeIds) : [...this.excludeIds, ...this.newestExcludeIds];
  }

  /**
   * Checks if the given layout element config has the newest autofill enabled.
   * @param config
   */
  isLayoutElementHasNewestAutoFilled(config: LayoutElementContentConfigurationGastroBase): boolean {
    return config.autoFill?.newest || false;
  }

  /**
   * Excludes a given id from the upcoming requests. Based on the newest autofill setting it will either add the id to the
   * newest excludeIds or the excludeIds.
   * @param config
   * @param id
   */
  excludeItem(config: LayoutElementContentConfigurationGastroBase, id: string): void {
    const isNewestAutoFilled = this.isLayoutElementHasNewestAutoFilled(config);
    if (isNewestAutoFilled) {
      this.newestExcludeIds.add(id);
    }
    this.excludeIds.add(id);
  }

  /**
   * Returns the indexes that are empty in the array and should be consumed by the autofill process.
   * @param config
   * @param consumeCount
   */
  findIndexesToConsume(config: LayoutElementContentConfigurationGastroBase, consumeCount: number): number[] {
    const occasions = config?.selectedOccasions || [];
    const occasionsCount = occasions.length || 0;
    const indexes: number[] = [];
    for (let i = 0; indexes.length <= consumeCount && i < occasionsCount; i++) {
      if (occasions[i]?.data?.id) {
        continue;
      }
      indexes.push(i);
    }
    return indexes;
  }

  /**
   * Puts the autofill items to empty indexes in the selected array. This is needed as the explicit items can be
   * anywhere in the array, so we have fill these empty slots.
   * @param config
   * @param items
   * @param consumeIndexes
   */
  fillAutofilledItems(
    config: LayoutElementContentConfigurationGastroBase,
    items: BasicItem[],
    consumeIndexes: number[]
  ): LayoutElementContentConfigurationGastroBaseSelectedOccasion[] {
    const occ = config?.selectedOccasions || [];
    const occasions = [...occ];
    const occasionsCount = occasions.length || 0;
    let consumedIndex = 0;
    for (let i = 0; i < occasionsCount; i++) {
      if (consumeIndexes.includes(i)) {
        occasions[i] = { ...occasions[i], data: items[consumedIndex] as any };
        consumedIndex++;
      }
    }
    return occasions;
  }

  /**
   * Returns the first x items of the given array.
   * @param array
   * @param x
   */
  getItems<T>(array: T[], x: number): T[] {
    return array.slice(0, x);
  }
}
