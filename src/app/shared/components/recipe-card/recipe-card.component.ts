import { ChangeDetectionStrategy, Component, HostBinding, inject, Input, OnInit } from '@angular/core';
import { CategoryLabelTypes, RecipeCardType } from '../../definitions';
import { BaseComponent, buildAuthorUrl, buildRecipeUrl, FocusPointDirective, RecipeCard } from '@trendency/kesma-ui';
import { RecipeDifficultyPipe } from '../../pipes';
import { UtilService } from '@trendency/kesma-core';
import { CategoryLabelComponent } from '../category-label/category-label.component';
import { RouterLink } from '@angular/router';
import { NgIf, NgSwitch, NgSwitchCase, NgTemplateOutlet } from '@angular/common';

@Component({
  selector: 'mindmegette-recipe-card',
  templateUrl: './recipe-card.component.html',
  styleUrls: ['./recipe-card.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [RecipeDifficultyPipe],
  imports: [Ng<PERSON><PERSON>, Ng<PERSON><PERSON>Case, NgTemplateOutlet, RouterLink, FocusPointDirective, NgIf, CategoryLabelComponent],
})
export class RecipeCardComponent extends BaseComponent<RecipeCard> implements OnInit {
  private readonly utils = inject(UtilService);

  @Input() isUsedInGuaranteeBox: boolean = false;

  @Input() set styleID(styleID: RecipeCardType) {
    this._type = styleID;
    this.hostClass = `style-${RecipeCardType[styleID]} ${this.isUsedInGuaranteeBox ? 'style-guarantee-box' : ''}`;
  }

  @Input() hasBackground: boolean = false;
  @Input() desktopWidth: number = 12; // Value from 1-12 to indicate the width of the card on desktop.
  @Input() isSidebar: boolean = false;
  @Input() isExperienceOccasionList: boolean = false;
  @Input() useLoadingAttribute: boolean = true;

  @HostBinding('class') hostClass = '';

  private _type = RecipeCardType.LeftImageShortCard;
  readonly RecipeCardType = RecipeCardType;
  readonly CategoryLabelTypes = CategoryLabelTypes;
  readonly breakpoint: number = 4;

  readonly buildAuthorUrl = buildAuthorUrl;

  constructor(private readonly difficultyPipe: RecipeDifficultyPipe) {
    super();
  }

  get isPriorityContent(): boolean {
    const isMobile = this.utils.isBrowser() && window.innerWidth < 992;
    return (isMobile && !!this.recipeCard.priorityContentMobile) || (!isMobile && !!this.recipeCard.priorityContentDesktop);
  }

  get styleID(): RecipeCardType {
    return this._type;
  }

  get recipeCard(): RecipeCard {
    return this.data as RecipeCard;
  }

  get recipeLink(): string[] {
    return buildRecipeUrl(this.data as RecipeCard);
  }

  get sentByUserLink(): string[] {
    return ['/', 'profil', this.data?.sentByUser?.id as string];
  }

  get recipeDifficulty(): string {
    if (typeof this.data?.difficulty !== 'string') {
      return this.difficultyPipe.transform(this.data?.difficulty?.title as unknown as string);
    } else {
      return this.difficultyPipe.transform(this.data?.difficulty as unknown as string);
    }
  }

  override ngOnInit(): void {
    super.ngOnInit();

    this.hostClass = this.isUsedInGuaranteeBox ? (this.hostClass += ' style-guarantee-box') : this.hostClass;
  }
}
