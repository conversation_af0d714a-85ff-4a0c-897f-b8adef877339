<div class="left">
  <h2 class="left-title">{{ data?.title }}</h2>
  <p class="left-text">{{ data?.description }}</p>
  <div class="left-wrapper">
    <a [routerLink]="['/', 'turpik']" class="left-wrapper-button green-button"> Megnézem az összes turpit<i class="icon mindmegette-icon-arrow-right"></i> </a>
    <button (click)="onSubmitClicked()" class="left-wrapper-button">Tu<PERSON><PERSON> feltöltése</button>
  </div>
</div>
<div class="right">
  <img [src]="data?.thumbnail?.url || 'assets/images/mme-placeholder.jpg'" [alt]="data?.thumbnail?.alt" class="background" loading="lazy" />
  <ng-template #itemTemplate let-turpi="data">
    <div class="turpi">
      <h3 *ngIf="turpi?.title" class="turpi-title">{{ turpi?.title }}</h3>
      <p class="turpi-description">{{ turpi?.description }}</p>
      <div *ngIf="turpi?.author?.name" class="turpi-author">
        <span *ngIf="!turpi?.author?.slug">{{ turpi?.author?.name }}</span>
        <a *ngIf="turpi?.author?.slug" class="turpi-author-link" [routerLink]="['/', 'profil', turpi?.author?.slug]">{{ turpi?.author?.name }}</a>
      </div>
    </div>
  </ng-template>
  <ng-template #previousNavigation><kesma-icon name="mindmegette-icon-white-left-arrow" /></ng-template>
  <ng-template #nextNavigation><kesma-icon name="mindmegette-icon-white-right-arrow" /></ng-template>
  <div
    kesma-swipe
    [itemTemplate]="itemTemplate"
    [previousNavigationTemplate]="previousNavigation"
    [nextNavigationTemplate]="nextNavigation"
    [useNavigation]="true"
    [usePagination]="true"
    [dataTrackByProperty]="'title'"
    [data]="data?.turpies"
    class="turpi-swiper"
    [breakpoints]="{ default: { itemCount: 1, gap: '16px' } }"
  ></div>
</div>
