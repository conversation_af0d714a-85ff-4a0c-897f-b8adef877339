import { ChangeDetectionStrategy, Component, ElementRef, Input, OnInit, signal } from '@angular/core';
import { BaseComponent, IconComponent, KesmaSwipeComponent, RecipeCard, SwipeBreakpoints } from '@trendency/kesma-ui';
import { RecipeCardType, RecipeListType } from '../../definitions';
import { RecipeCardComponent } from '../recipe-card/recipe-card.component';
import { NgSwitch, NgSwitchCase } from '@angular/common';

@Component({
  selector: 'mindmegette-recipe-list',
  templateUrl: './recipe-list.component.html',
  styleUrls: ['./recipe-list.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgSwitch, NgSwitchCase, RecipeCardComponent, KesmaSwipeComponent, IconComponent],
})
export class RecipeListComponent extends BaseComponent<RecipeCard[]> implements OnInit {
  @Input() desktopWidth: number = 12; // Value from 1-12 to indicate the width of the card on desktop.
  @Input() styleID = RecipeListType.BasicList;
  @Input() isSidebar: boolean = false;
  @Input() selectedTagIndex: number = 0;
  @Input() isExperienceOccasionList: boolean = false;
  @Input() isGastroPage = false;
  @Input() useLoadingAttribute: boolean = true;
  breakpoints = signal<SwipeBreakpoints | null>(null);
  readonly breakpoint: number = 4;

  readonly RecipeCardType = RecipeCardType;
  readonly RecipeListType = RecipeListType;

  constructor(private readonly elementRef: ElementRef) {
    super();
  }

  override ngOnInit(): void {
    super.ngOnInit();
    this.swiperOptionSetter();
  }

  swiperOptionSetter(): void {
    if (this.isGastroPage) {
      this.breakpoints.set({
        default: {
          itemCount: 1.25,
          gap: '16px',
        },
        1024: {
          itemCount: 4,
        },
        576: {
          itemCount: 2,
        },
      });
      return;
    }
    if (this.styleID === RecipeListType.RecipeListWithDetails) {
      this.breakpoints.set({
        default: {
          itemCount: 1,
          gap: '24px',
        },
      });
      return;
    }
    if (this.elementRef?.nativeElement?.offsetWidth <= 576 && this.elementRef?.nativeElement?.offsetWidth !== 0) {
      this.breakpoints.set({
        default: {
          itemCount: 1,
          gap: '24px',
        },
      });
      return;
    }
    if (this.desktopWidth > this.breakpoint) {
      this.breakpoints.set({
        default: {
          itemCount: 4,
          gap: '40px',
        },
        1024: {
          itemCount: 4,
        },
        576: {
          itemCount: 2,
          gap: '16px',
        },
        200: {
          itemCount: this.isGastroPage ? 1.25 : 1,
        },
      });
      return;
    } else {
      this.breakpoints.set({
        default: {
          itemCount: 1,
          gap: '24px',
        },
      });
    }
  }
}
