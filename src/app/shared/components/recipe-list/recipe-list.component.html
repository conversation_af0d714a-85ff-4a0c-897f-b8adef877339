<ng-template #itemTemplate let-data="data">
  <ng-container [ngSwitch]="styleID">
    <ng-container *ngSwitchCase="RecipeListType.BasicList">
      <mindmegette-recipe-card
        [useLoadingAttribute]="useLoadingAttribute"
        [data]="data"
        [hasBackground]="true"
        [styleID]="RecipeCardType.TopImageLeftAlignedCard"
      ></mindmegette-recipe-card>
    </ng-container>
    <!-- Details list -->
    <ng-container *ngSwitchCase="RecipeListType.RecipeListWithDetails">
      <mindmegette-recipe-card
        [useLoadingAttribute]="useLoadingAttribute"
        [styleID]="RecipeCardType.TopImageCenteredCard"
        [isSidebar]="isSidebar"
        [isExperienceOccasionList]="isExperienceOccasionList"
        [data]="data"
      ></mindmegette-recipe-card>
    </ng-container>
  </ng-container>
</ng-template>
<ng-template #previousNavigation>
  <kesma-icon name="mindmegette-icon-green-left-arrow" />
</ng-template>
<ng-template #nextNavigation>
  <kesma-icon name="mindmegette-icon-green-right-arrow" />
</ng-template>
<div
  kesma-swipe
  [itemTemplate]="itemTemplate"
  [previousNavigationTemplate]="previousNavigation"
  [nextNavigationTemplate]="nextNavigation"
  [useNavigation]="true"
  [usePagination]="true"
  [data]="data"
  [breakpoints]="breakpoints()"
></div>
