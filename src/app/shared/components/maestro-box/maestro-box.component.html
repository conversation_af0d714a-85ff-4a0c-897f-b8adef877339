<div class="maestro-container-wrapper">
  <div class="maestro-container">
    <div class="maestro-content">
      <h3 class="title">{{ data?.title }}</h3>
      <strong class="maestro-name">{{ selectedMaestro?.name }}</strong>
      <p class="description">{{ selectedMaestro?.introduction }}</p>
      <a class="show-maestro" [href]="onViewMaestro()"> Megnézem a Maestrót <i class="icon mindmegette-icon-black-right-arrow"></i> </a>
    </div>
    <div class="recipe-container">
      <ng-container *ngFor="let cardData of selectedMaestro?.selectedArticlesAndRecipes; trackBy: trackByFn">
        <mindmegette-recipe-card
          *ngIf="cardData.type === cardType.RECIPE"
          [data]="$any(cardData)"
          [styleID]="recipeCardType"
          [hasBackground]="true"
        ></mindmegette-recipe-card>
        <mindmegette-article-card
          *ngIf="cardData.type === cardType.ARTICLE"
          [data]="$any(cardData)"
          [styleID]="articleCardType"
          [hasBackground]="true"
        ></mindmegette-article-card>
      </ng-container>
    </div>
  </div>
</div>

<div class="maestro-swiper-container">
  <ng-template #itemTemplate let-element="data" let-index="index">
    <div [class.active]="index === selectedMaestroIndex" (click)="onMaestroSelect(index)">
      <div class="element">
        <div class="image-container">
          <img [alt]="element?.name + 'avatár kép'" loading="lazy" [src]="element?.avatarUrl || './assets/images/author-placeholder.svg'" class="thumbnail" />
          <img
            *ngIf="element.verified"
            [alt]="'verified'"
            loading="lazy"
            [src]="'./assets/images/icons/mindmegette-icon-verified-with-background.svg'"
            class="verified"
          />
        </div>
        <div>
          <h3 class="title">{{ element?.name }}</h3>
          <p class="description">{{ element.rank }}</p>
        </div>
      </div>
    </div>
  </ng-template>
  <ng-template #previousNavigation><kesma-icon name="mindmegette-icon-green-left-arrow" aria-label="Előző" /></ng-template>
  <ng-template #nextNavigation><kesma-icon name="mindmegette-icon-green-right-arrow" aria-label="Következő" /></ng-template>
  <div
    kesma-swipe
    [itemTemplate]="itemTemplate"
    [previousNavigationTemplate]="previousNavigation"
    [nextNavigationTemplate]="nextNavigation"
    [useNavigation]="true"
    [data]="data?.authors"
    [breakpoints]="breakpoints"
  ></div>
</div>
