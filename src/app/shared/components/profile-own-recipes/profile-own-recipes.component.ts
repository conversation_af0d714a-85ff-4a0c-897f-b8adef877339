import { ChangeDetectionStrategy, ChangeDetectorRef, Component, inject, Input, OnDestroy, OnInit } from '@angular/core';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { ActivatedRoute, Params, Router } from '@angular/router';
import { IMetaData, SchemaOrgService, SeoService } from '@trendency/kesma-core';
import {
  AdvertisementAdoceanComponent,
  AdvertisementAdoceanStoreService,
  ApiResponseMetaList,
  createCanonicalUrlForPageablePage,
  LimitableMeta,
  PAGE_TYPES,
  RecipeCard,
} from '@trendency/kesma-ui';
import { Subject } from 'rxjs';
import { map, takeUntil } from 'rxjs/operators';
import { environment } from '../../../../environments/environment';
import { backendRecipeToRecipe, createMMETitle, getStructuredDataForRecipeList } from '../../utils';
import { defaultMetaInfo } from '../../constants';
import { OwnRecipesType, RecipeCardType } from '../../definitions';
import { ProfileOwnRecipesService } from '../../services';
import { SearchBarComponent } from '../search-bar/search-bar.component';
import { NgClass, NgFor, NgIf } from '@angular/common';
import { TagAsButtonComponent } from '../tag-as-button/tag-as-button.component';
import { RecipeCardComponent } from '../recipe-card/recipe-card.component';
import { MindmegettePagerComponent } from '../pager/mindmegette-pager.component';

@Component({
  selector: 'app-profile-own-recipes',
  templateUrl: './profile-own-recipes.component.html',
  styleUrls: ['./profile-own-recipes.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgClass, SearchBarComponent, NgFor, AdvertisementAdoceanComponent, NgIf, TagAsButtonComponent, RecipeCardComponent, MindmegettePagerComponent],
})
export class ProfileOwnRecipesComponent implements OnInit, OnDestroy {
  @Input() setMeta = true;
  @Input() showPageInTitle = false;
  @Input() title = 'Saját receptjeim';
  @Input() displayType: OwnRecipesType = OwnRecipesType.OWN_PROFILE;
  private readonly adStore = inject(AdvertisementAdoceanStoreService);
  readonly OwnRecipesType: OwnRecipesType;
  recipes: RecipeCard[];
  limitable: LimitableMeta;

  searchBarVisible = false;
  sortBarVisible = false;
  sortList = this.ownRecipesService.sortList;

  readonly adverts = toSignal(
    this.adStore.advertisemenets$.pipe(
      map((ads) => this.adStore.separateAdsByMedium(ads, PAGE_TYPES.recipe_pages)),
      takeUntilDestroyed()
    )
  );

  private readonly destroySubject$ = new Subject<void>();
  readonly RecipeCardType = RecipeCardType;

  constructor(
    private readonly seo: SeoService,
    private readonly ownRecipesService: ProfileOwnRecipesService,
    private readonly cdr: ChangeDetectorRef,
    private readonly route: ActivatedRoute,
    private readonly router: Router,
    private readonly schemaService: SchemaOrgService
  ) {}

  ngOnInit(): void {
    if (this.setMeta) {
      this.setMetaData();
    }

    this.ownRecipesService.recipes$.pipe(takeUntil(this.destroySubject$)).subscribe(({ data, meta }) => {
      this.recipes = data?.map(backendRecipeToRecipe);
      this.limitable = (meta as ApiResponseMetaList)?.limitable;

      this.schemaService.removeStructuredData();
      if (this.recipes?.length) {
        this.schemaService.insertSchema(getStructuredDataForRecipeList(this.recipes, environment?.siteUrl ?? ''));
      }

      this.cdr.markForCheck();
    });
  }

  ngOnDestroy(): void {
    this.destroySubject$.next();
    this.destroySubject$.complete();
  }

  onFilterChange(sort: Record<string, string>): void {
    this.queryParamChanged(sort);
  }

  onSortChange(index: number): void {
    const sortBy = this.sortList[index];
    this.queryParamChanged({ sort: sortBy.slug });
  }

  queryParamChanged(queryParams: Params): void {
    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: queryParams,
      queryParamsHandling: 'merge',
      skipLocationChange: true,
    });
  }

  private setMetaData(): void {
    const canonical = createCanonicalUrlForPageablePage('profil/sajat-receptek');
    if (canonical) this.seo.updateCanonicalUrl(canonical);
    const title = createMMETitle('Profil - Saját receptek');
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
    };
    this.seo.setMetaData(metaData);
  }
}
