@use 'shared' as *;

:host {
  display: block;
  width: 100%;

  ::ng-deep {
    --kui-swipe-pagination-dot-color: var(--kui-white);

    .pagination {
      position: unset;
      width: fit-content;
    }

    .swiper-pagination-bullet {
      width: 10px;
      height: 10px;
      margin: 0 8px !important;
      background-color: var(--kui-white);
    }

    .style-TopImageLeftAlignedCard .title {
      margin-bottom: 12px;
      font-size: 20px;
      min-height: 40px;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
    }
  }

  .box {
    display: flex;
    flex-direction: row;
    width: 100%;
    border-radius: 12px;

    @include media-breakpoint-down(sm) {
      flex-direction: column;
    }

    &-left {
      display: flex;
      flex: 1;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      gap: 40px;
      padding: 64px 48px;
      background-color: var(--kui-green-700);
      border-radius: 16px 0 0 16px;
      @include media-breakpoint-down(sm) {
        padding: 32px 16px;
        border-radius: unset;
        align-items: center;
        border-top-left-radius: 12px;
        border-top-right-radius: 12px;
      }

      &-title {
        display: flex;
        align-items: center;

        @include media-breakpoint-down(sm) {
          flex-direction: column;
          gap: 16px;
        }

        .icon {
          width: 32px;
          height: 32px;
          margin-right: 8px;
        }

        h2 {
          color: var(--kui-white);
          font-size: 34px;
          font-weight: 600;
          line-height: 40px;
          letter-spacing: -0.17px;
          @include media-breakpoint-down(sm) {
            font-size: 24px;
            font-weight: 600;
            line-height: 28px;
            letter-spacing: 0.12px;
          }
        }
      }

      &-description {
        font-family: var(--kui-font-secondary);
        font-size: 18px;
        font-weight: 400;
        line-height: 28px;
        color: var(--kui-white);
      }

      &-button {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 6px;
        padding: 10px 20px;
        background-color: var(--kui-white);
        border-radius: 8px;
        font-family: var(--kui-font-secondary);
        font-size: 16px;
        font-weight: 500;
        line-height: 24px;
        letter-spacing: 0.16px;
        color: var(--kui-gray-950);
        @include media-breakpoint-down(sm) {
          white-space: nowrap;
        }

        .icon {
          width: 20px;
          height: 20px;
        }
      }
    }

    &-right {
      position: relative;
      flex: 1;

      & > .background {
        position: absolute;
        z-index: 0;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        object-fit: cover;
        object-position: left;
        border-radius: 0 16px 16px 0;

        @include media-breakpoint-down(sm) {
          border-radius: unset;
          border-bottom-left-radius: 12px;
          border-bottom-right-radius: 12px;
        }
      }

      &-wrapper {
        position: relative;
        width: 100%;
        display: flex;
        justify-content: center;
        padding-top: 50px;
        padding-bottom: 20px;

        &-recipes {
          display: block;
          width: 50%;
          max-height: 100%;

          @include media-breakpoint-down(md) {
            width: 75%;
          }

          @include media-breakpoint-down(sm) {
            width: 50%;
          }

          @include media-breakpoint-down(xs) {
            padding-top: 40px;
            width: 85%;
          }
        }
      }

      &-navigation {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        margin: 14px 0;

        &:has(.swiper-pagination-lock) {
          display: none;
        }

        .icon {
          width: 24px;
          height: 24px;
        }
      }
    }
  }

  &.style-Double {
    .box {
      padding: 40px 28px;
      background-color: var(--kui-green-700);
      display: flex;
      flex-direction: row;
      gap: 60px;

      @include media-breakpoint-down(sm) {
        flex-direction: column;
        gap: 0;
        padding: 16px 16px 0 16px;
        border-radius: 12px;
      }

      &-left {
        padding: unset;
        align-items: flex-start;

        &-title {
          .icon {
            display: none;
          }
        }
      }

      &-right {
        &-wrapper {
          margin: 10px 0;
          display: flex;
          justify-content: center;
          &-recipes {
            width: 85%;

            @include media-breakpoint-down(sm) {
              width: 75%;
            }

            @include media-breakpoint-down(xs) {
              width: 90%;
            }
          }
        }

        &-navigation {
          @include media-breakpoint-up(md) {
            margin: 14px 0 0 0;
          }
        }
      }
    }
  }

  @include media-breakpoint-down(sm) {
    .navigation-button {
      display: none;
    }
  }
}
