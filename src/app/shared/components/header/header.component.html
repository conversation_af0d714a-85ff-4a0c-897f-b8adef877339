<header class="header">
  <section class="top-header">
    <div class="top-header-wrapper">
      <div class="top-header-wrapper-icons">
        <i
          (click)="toggleMobileMenu()"
          [ngClass]="isMobileMenuOpen ? 'icon mindmegette-header-close-hamburger-icon' : 'icon mindmegette-header-hamburger-icon'"
        ></i>
        <div class="top-header-logo">
          <h1 *ngIf="isHomePage">
            <img (click)="scrollToTop()" [src]="'/assets/images/mindmegette-logo.svg'" width="178" height="58" alt="Mindmegette" loading="eager" />
          </h1>
          <a [routerLink]="['/']" *ngIf="!isHomePage">
            <img [src]="'/assets/images/mindmegette-logo.svg'" width="178" height="58" alt="Mindmegette" loading="eager" />
          </a>
        </div>
      </div>
      <div class="top-header-search-bar">
        <app-header-search-bar></app-header-search-bar>
      </div>
      <div class="top-header-actions">
        <button (click)="isMobileMenuOpen = false; updateBodyOverflow(); openAuthenticatedRoute('/receptbekuldes')" class="top-header-actions-send-recipe">
          Receptet küldök
        </button>
        <button
          (click)="isMobileMenuOpen = false; updateBodyOverflow(); openAuthenticatedRoute('/profil/bevasarlolista')"
          class="top-header-actions-icon"
          aria-label="Bevásárlólista"
        >
          <i class="icon mindmegette-cart-icon"></i>
        </button>
        <button *ngIf="!user" (click)="redirectToLogin(); isMobileMenuOpen = false" class="top-header-actions-icon" aria-label="Bejelentkezés">
          <i class="icon mindmegette-profile-icon"></i>
        </button>
        <app-header-profile-menu [user]="user"></app-header-profile-menu>
      </div>
    </div>
  </section>
  <section class="bottom-header">
    <div class="mobile-menu-bottom-header">
      <div class="mobile-menu-bottom-header-search-bar">
        <app-header-search-bar></app-header-search-bar>
      </div>
    </div>
    <div class="bottom-header-wrapper">
      <ng-template #itemTemplate let-item let-index="index">
        <ng-container *ngTemplateOutlet="(item.children ?? []).length > 0 ? parent : basicLink; context: { item, index: index }"></ng-container>
      </ng-template>
      <ng-template #rightItemTemplate let-item let-index="index">
        <ng-container *ngTemplateOutlet="basicLink; context: { item, index, isRightMenu: true }"></ng-container>
      </ng-template>
      <app-header-scrollable-menu class="bottom-header-menu" [data]="mainMenu" [itemTemplate]="itemTemplate" [gap]="20"></app-header-scrollable-menu>
      <app-header-scrollable-menu class="bottom-header-right-menu" [data]="rightMenu" [itemTemplate]="rightItemTemplate" [gap]="8"></app-header-scrollable-menu>
    </div>
  </section>

  <app-sticky-profile-header *ngIf="showStickyHeader && !isMobileMenuOpen" [user]="user"></app-sticky-profile-header>

  <ng-container *ngIf="selectedMenuItem$ | async as selectedItem">
    <app-header-menu
      *ngIf="!isMobileMenuOpen"
      (linkClickEvent)="handleMenuLinkClick()"
      (clickOutsideEvent)="activateItem(selectedItem)"
      [menuItem]="selectedItem"
      class="desktop"
    ></app-header-menu>
  </ng-container>

  <!-- MOBILE-->
  <nav *ngIf="isMobileMenuOpen" [ngClass]="hasBreakingNews ? 'mobile-menu-increased-padding' : 'mobile-menu'">
    <div *ngIf="tags?.length" class="mobile-menu-tags">
      <mindmegette-tag (click)="closeHamburgerMenu()" [data]="tags"></mindmegette-tag>
    </div>
    <nav class="mobile-menu-right-menu">
      <ng-container *ngFor="let menuItem of rightMenu; let index = index" class="bottom-header-right-menu-item">
        <ng-container *ngTemplateOutlet="basicLink; context: { item: menuItem, index: index, isRightMenu: true }"></ng-container>
      </ng-container>
    </nav>
    <ul class="mobile-menu-list">
      <ng-container [ngSwitch]="currentLevel">
        <ng-container *ngSwitchCase="MobileMenuLevels.FirstLevel">
          <li *ngFor="let menuItem of mainMenu" class="mobile-menu-list-item">
            <ng-container *ngIf="(menuItem.children ?? []).length > 0">
              <ng-container *ngTemplateOutlet="firstLevelMenu; context: { item: menuItem }"></ng-container>
            </ng-container>
            <ng-container *ngIf="(menuItem.children ?? []).length === 0">
              <ng-container *ngTemplateOutlet="mobileLink; context: { item: menuItem, class: 'mobile-menu-list-item-link' }"></ng-container>
            </ng-container>
          </li>
        </ng-container>
        <ng-container *ngSwitchCase="MobileMenuLevels.SecondLevel">
          <ng-container *ngTemplateOutlet="secondLevelMenu"></ng-container>
        </ng-container>
        <ng-container *ngSwitchCase="MobileMenuLevels.ThirdLevel">
          <ng-container *ngTemplateOutlet="thirdLevelMenu"></ng-container>
        </ng-container>
      </ng-container>
    </ul>
    <ng-container *ngIf="fullArticles.length > 0">
      <div class="guaranteed-recipes-title">
        <i class="icon mindmegette-small-emblem"></i>
        Garancia
      </div>
      <div class="mobile-menu-container">
        <div class="mobile-menu-inner-container">
          <div class="mobile-menu-article-card" *ngFor="let article of fullArticles">
            <mindmegette-article-card
              *ngIf="article?.relatedType === 'FullArticle'"
              (click)="toggleMobileMenu()"
              [data]="relatedArticleToArticleCard(article?.related!)"
              [styleID]="articleCardType.TopImageLeftAlignedCard"
            >
            </mindmegette-article-card>
            <mindmegette-recipe-card
              class="recipe"
              *ngIf="article?.relatedType === 'FullRecipe'"
              (click)="toggleMobileMenu()"
              [data]="relatedArticleToRecipeCard(article?.related!)"
              [styleID]="recipeCardType.TopImageLeftAlignedCard"
            >
            </mindmegette-recipe-card>
          </div>
        </div>
      </div>
    </ng-container>
    <div class="mobile-menu-send-recipe">
      <a (click)="isMobileMenuOpen = false; updateBodyOverflow(); openAuthenticatedRoute('/receptbekuldes')" class="mobile-menu-send-recipe-button"
        >Receptet küldök</a
      >
    </div>
  </nav>
</header>

<!-- Link templates -->
<ng-template #parent let-item="item">
  <!-- FONTOS: firstMenuLevel, firstMenuLevelArrow id-kat ne módosítsd, szükségesek a clickOutside működéséhez!   -->
  <p id="firstMenuLevel" (click)="activateItem(item)" [ngClass]="{ active: selectedMenuItem$.value?.id === item.id }" class="bottom-header-menu-list-item-link">
    {{ item.title }}
    <kesma-icon
      [ngClass]="{ active: selectedMenuItem$.value?.id === item.id }"
      [name]="'header-arrow-right'"
      [size]="20"
      [customId]="'firstMenuLevelArrow'"
    ></kesma-icon>
  </p>
</ng-template>
<ng-template #basicLink let-item="item" let-isRightMenu="isRightMenu">
  <span [ngSwitch]="item.relatedType" [ngClass]="isRightMenu ? 'bottom-header-right-menu-item' : 'bottom-header-menu-list-item-link'">
    <ng-container *ngSwitchCase="relatedTypes.ARTICLE">
      <a *ngIf="item.target !== '_blank'" (click)="closeHamburgerMenu()" [routerLink]="buildArticleUrl(item.related)">
        {{ item.title }}
      </a>
      <a *ngIf="item.target === '_blank'" [target]="item.target" (click)="closeHamburgerMenu()" [href]="buildBlankUrlFromArray(item.link)">
        {{ item.title }}
      </a>
    </ng-container>
    <a *ngSwitchCase="relatedTypes.CUSTOM_URL" [target]="item.target" (click)="closeHamburgerMenu()" [href]="item.link">
      <ng-container *ngIf="item.title === 'andante piskóta desszerttár' || item.title === 'ANDANTE PISKÓTA DESSZERTTÁR'">
        <i class="icon andante-button"></i>
      </ng-container>
      {{ item.title }}
    </a>
    <ng-container *ngSwitchDefault>
      <ng-container *ngIf="item.link?.length > 0 && !item.link.includes('404'); else spanItem">
        <a *ngIf="item.target !== '_blank'" (click)="closeHamburgerMenu()" [routerLink]="item.link">
          {{ item.title }}
        </a>
        <a *ngIf="item.target === '_blank'" [target]="item.target" (click)="closeHamburgerMenu()" [href]="buildBlankUrlFromArray(item.link)">
          {{ item.title }}
        </a>
      </ng-container>
    </ng-container>
    <ng-template #spanItem>
      <span>{{ item.title }}</span>
    </ng-template>
  </span>
</ng-template>
<ng-template #firstLevelMenu let-item="item">
  <p
    (click)="activateItem(item, MobileMenuLevels.SecondLevel)"
    [ngClass]="{ active: selectedMenuItem$.value?.id === item.id }"
    class="mobile-menu-list-item-link"
  >
    {{ item.title }}
    <kesma-icon [name]="'header-arrow-right'" [size]="20"></kesma-icon>
  </p>
</ng-template>

<ng-template #secondLevelMenu>
  <ng-container *ngIf="selectedMenuItem$ | async as selectedMenuItem">
    <p
      (click)="activateItem(null, MobileMenuLevels.FirstLevel)"
      [ngClass]="{ active: selectedMenuItem$.value?.id === selectedMenuItem.id }"
      class="mobile-menu-list-item-link justify-content-flex-start"
    >
      <kesma-icon [name]="'header-arrow-right'" [size]="20"></kesma-icon>
      {{ selectedMenuItem.title }}
    </p>
    <li *ngFor="let menuItem of selectedMenuItem?.children" class="mobile-menu-list-item">
      <ng-container *ngIf="(menuItem?.children ?? []).length > 0">
        <div
          (click)="activateItem(menuItem, MobileMenuLevels.ThirdLevel, selectedMenuItem)"
          [ngClass]="menuItem?.isHighlighted ? 'mobile-menu-list-item-link-highlighted' : 'mobile-menu-list-item-link'"
          class="mobile-menu-list-item-link"
        >
          <div class="title-wrapper">
            <i *ngIf="menuItem?.isHighlighted" class="icon mindmegette-header-highlighted-item"></i>
            <span [ngClass]="{ highlighted: menuItem?.isHighlighted }">{{ menuItem.title }}</span>
          </div>
          <kesma-icon [name]="'header-arrow-right'" [size]="20"></kesma-icon>
        </div>
      </ng-container>
      <ng-container *ngIf="(menuItem?.children ?? []).length === 0">
        <ng-container *ngTemplateOutlet="mobileLink; context: { item: menuItem, class: 'mobile-menu-list-item-link' }"></ng-container>
      </ng-container>
    </li>
  </ng-container>
</ng-template>

<ng-template #thirdLevelMenu>
  <ng-container *ngIf="selectedMenuItem$ | async as selectedMenuItem">
    <div
      (click)="activateItem(parentMenuItem!, MobileMenuLevels.SecondLevel)"
      [ngClass]="{ active: selectedMenuItem$.value?.id === selectedMenuItem.id, highlighted: selectedMenuItem?.isHighlighted }"
      class="mobile-menu-list-item-link justify-content-flex-start"
    >
      <kesma-icon [name]="'header-arrow-right'" [size]="20"></kesma-icon>
      <div class="title-wrapper">
        <i *ngIf="selectedMenuItem?.isHighlighted" class="icon mindmegette-header-highlighted-item"></i>
        <span [ngClass]="{ highlighted: selectedMenuItem?.isHighlighted }">{{ selectedMenuItem.title }}</span>
      </div>
    </div>
    <li *ngFor="let menuItem of selectedMenuItem?.children" class="mobile-menu-list-item">
      <ng-container *ngIf="(menuItem?.children ?? []).length > 0">
        <p class="mobile-menu-list-item-category">
          {{ menuItem.title }}
        </p>
        <p *ngFor="let item of menuItem?.children">
          <ng-container *ngIf="item.relatedType !== 'FullArticle' && item.relatedType !== 'FullRecipe'">
            <ng-container *ngTemplateOutlet="mobileLink; context: { item: item, class: 'mobile-menu-list-item-sub-category' }"></ng-container>
          </ng-container>
        </p>
      </ng-container>
      <ng-container *ngIf="(menuItem?.children ?? []).length === 0">
        <ng-container *ngTemplateOutlet="mobileLink; context: { item: menuItem, class: 'mobile-menu-list-item-category' }"></ng-container>
      </ng-container>
    </li>
  </ng-container>
</ng-template>

<ng-template #mobileLink let-class="class" let-item="item">
  <ng-container *ngTemplateOutlet="!item.isCustomUrl ? basicMobileLink : customMobileUrl; context: { item: item, class: class }"></ng-container>
</ng-template>
<ng-template #basicMobileLink let-class="class" let-item="item">
  <a (click)="closeHamburgerMenu()" [class]="class" [routerLink]="item.link" [target]="item.target">{{ item.title }}</a>
</ng-template>
<ng-template #customMobileUrl let-class="class" let-item="item">
  <a (click)="closeHamburgerMenu()" [class]="class" [href]="item.link" [target]="item.target">
    {{ item.title }}
  </a>
</ng-template>
