import { CommonModule, NgForOf, NgIf } from '@angular/common';
import { ChangeDetectionStrategy, Component } from '@angular/core';
import { SponsoredQuizComponent as KesmaSponsoredQuizComponent } from '@trendency/kesma-ui';

@Component({
  selector: 'app-sponsored-quiz',
  templateUrl: '../../../../../node_modules/@trendency/kesma-ui/src/lib/components/sponsored-quiz/sponsored-quiz.component.html',
  styleUrls: [
    '../../../../../node_modules/@trendency/kesma-ui/src/lib/components/sponsored-quiz/sponsored-quiz.component.scss',
    './sponsored-quiz.component.scss',
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  imports: [NgForOf, NgIf, CommonModule],
})
export class SponsoredQuizComponent extends KesmaSponsoredQuizComponent {
  override get placeholderImage(): string {
    return './assets/images/placeholder.jpg';
  }
}
