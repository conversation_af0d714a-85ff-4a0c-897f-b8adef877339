import { Async<PERSON>ip<PERSON>, NgIf } from '@angular/common';
import { ChangeDetectionStrategy, Component, Inject, OnD<PERSON>roy, OnInit, Optional } from '@angular/core';
import { RouterLink } from '@angular/router';
import { RESPONSE, SeoService, UtilService } from '@trendency/kesma-core';
import { AdvertisementAdoceanStoreService, RecipeCard } from '@trendency/kesma-ui';
import type { Response } from 'express';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { defaultMetaInfo } from '../../../constants';
import { ApiService } from '../../../services';
import { backendRecipeToRecipe } from '../../../utils';
import { RecipeListComponent } from '../../recipe-list/recipe-list.component';

@Component({
  selector: 'app-404',
  templateUrl: './404.component.html',
  styleUrls: ['./404.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [RouterLink, NgIf, AsyncPipe, RecipeListComponent],
})
export class Error404Component implements OnInit, OnDestroy {
  recipes$: Observable<RecipeCard[]> = this.apiService.getRecipes$(4).pipe(map((recipe) => recipe.map(backendRecipeToRecipe)));

  constructor(
    private readonly seo: SeoService,
    private readonly utilsService: UtilService,
    private readonly apiService: ApiService,
    private readonly adStore: AdvertisementAdoceanStoreService,
    @Inject(RESPONSE) @Optional() private readonly response: Response
  ) {}

  ngOnInit(): void {
    this.adStore.disableAds();

    this.seo.setMetaData(
      {
        ...defaultMetaInfo,
        title: `404 - ${defaultMetaInfo.ogTitle}`,
        ogTitle: `404 - ${defaultMetaInfo.ogTitle}`,
        robots: '',
      },
      {
        canRobotsBeEmpty: true,
      }
    );

    if (!this.utilsService.isBrowser() && this.response) {
      this.response.status(404);
    }
  }

  ngOnDestroy(): void {
    this.adStore.enableAds();
  }
}
