<header>
  <section class="header-top">
    <div class="header-top-logo-wrapper">
      <div class="hamburger-menu">
        <i
          (click)="toggleMobileMenu()"
          [ngClass]="isMobileMenuOpen ? 'icon mindmegette-header-close-hamburger-icon' : 'icon mindmegette-header-hamburger-icon'"
        ></i>
      </div>
      <div class="header-top-logo-wrapper-with-highlighted-sponsor">
        <a (click)="onLogoClicked()" [routerLink]="'/elmenyek'">
          <img [src]="'/assets/images/gastro-logo.svg'" min-width="118" alt="Élmények logo" class="gastro-logo" />
        </a>
        @if (sponsorship$ | async; as sponsorship) {
          <ng-container *ngTemplateOutlet="highlightedSponsorThumbnail; context: { sponsorship, isHeaderBottom: false }"></ng-container>
        }
      </div>
    </div>
    <div class="search">
      <img [src]="'/assets/images/icons/magnifier-icon-black.svg'" alt="Nagyító ikon" />
      <input
        [formControl]="searchInput"
        (input)="search(searchInput.value)"
        (keydown)="onKeyDown($event)"
        type="text"
        class="search-bar-input"
        placeholder="Eseményt keresek"
      />
      <ng-container *ngIf="liveSearchResults$ | async as liveSearchResults">
        <ng-container *ngIf="liveSearchResults?.data?.length > 0">
          <ng-container *ngTemplateOutlet="searchResults; context: { liveSearchResults: liveSearchResults }"></ng-container>
        </ng-container>
      </ng-container>
    </div>
    <div class="header-top-right">
      <a class="mme-button" [routerLink]="'/'">
        <img [src]="'/assets/images/mindmegette-logo.svg'" alt="Mindmegette logo" />
        <kesma-icon [name]="'header-arrow-right'" [size]="20"></kesma-icon>
      </a>
      <button class="header-top-actions-icon search-button" (click)="toggleMobileSearchBar()" aria-label="Keresés">
        <img [src]="'/assets/images/icons/magnifier-icon-black.svg'" alt="Keresés" />
      </button>
      <a *ngIf="!user" [routerLink]="['/bejelentkezes']" (click)="isMobileMenuOpen = false" class="header-top-actions-icon" aria-label="Bejelentkezés">
        <i class="icon mindmegette-profile-icon"></i>
      </a>
      <ng-container *ngIf="user">
        <app-header-profile-menu [user]="user"></app-header-profile-menu>
      </ng-container>
    </div>
  </section>
  <section class="header-bottom">
    <ng-template #itemTemplate let-item let-index="index">
      <ng-container *ngTemplateOutlet="basicLink; context: { item, index: index }"></ng-container>
    </ng-template>
    <ng-template #tagTemplate let-item let-index="index">
      <ng-container *ngTemplateOutlet="tagLink; context: { item, index: index }"></ng-container>
    </ng-template>
    <app-header-scrollable-menu class="header-bottom-menu-list" [data]="mainMenu" [itemTemplate]="itemTemplate" [gap]="20"></app-header-scrollable-menu>
    <app-header-scrollable-menu class="header-bottom-tags" [data]="rightMenu" [itemTemplate]="tagTemplate" [gap]="8"></app-header-scrollable-menu>
  </section>
  <div *ngIf="isMobileSearchBarOpen" class="mobile-search">
    <div class="mobile-search-bar">
      <button class="search-toggle-mobile" (click)="toggleMobileSearchBar()">
        <img [src]="'/assets/images/icons/magnifier-icon-black.svg'" alt="Nagyító ikon" />
      </button>
      <input
        [formControl]="searchInput"
        (input)="search(searchInput.value)"
        (keydown)="onKeyDown($event)"
        type="text"
        class="search-bar-input"
        placeholder="Eseményt keresek"
      />

      <ng-container *ngIf="liveSearchResults$ | async as liveSearchResults">
        <ng-container *ngIf="liveSearchResults?.data?.length > 0">
          <ng-container *ngTemplateOutlet="searchResults; context: { liveSearchResults: liveSearchResults }"></ng-container>
        </ng-container>
      </ng-container>
    </div>
  </div>

  <!-- MOBILE MENU -->
  <nav *ngIf="isMobileMenuOpen" class="mobile-menu">
    <div class="mobile-menu-list">
      <li *ngFor="let menuItem of fullMenuList; let index = i" class="mobile-menu-list-item">
        <ng-container *ngIf="(menuItem.children ?? []).length < 1">
          <ng-container *ngTemplateOutlet="mobileLink; context: { menuItem, index }"></ng-container>
        </ng-container>
      </li>
    </div>
  </nav>
</header>
@if (sponsorship$ | async; as sponsorship) {
  <div class="highlighted-sponsor" [style.background-color]="sponsorship?.highlightedColor || '#DA0112'">
    <span class="highlighted-sponsor-title" [style.color]="sponsorship?.fontColor || 'var(--kui-white)'">{{ sponsorship?.title || 'Kiemelt támogatónk' }}</span>
    <ng-container *ngTemplateOutlet="highlightedSponsorThumbnail; context: { sponsorship, isHeaderBottom: true }"></ng-container>
  </div>
}

<ng-template #highlightedSponsorThumbnail let-sponsorship="sponsorship" let-isHeaderBottom="isHeaderBottom">
  @if (sponsorship?.url) {
    <a [href]="sponsorship.url" target="_blank">
      <img
        class="highlighted-sponsor-thumbnail"
        [class.header-bottom]="isHeaderBottom"
        *ngIf="sponsorship?.logoImage as logoImage"
        [src]="logoImage"
        [alt]="sponsorship?.title || 'Élmények kiemelt szponzor logó'"
        loading="lazy"
      />
    </a>
  } @else {
    <div>
      <img
        class="highlighted-sponsor-thumbnail"
        [class.header-bottom]="isHeaderBottom"
        *ngIf="sponsorship?.logoImage as logoImage"
        [src]="logoImage"
        [alt]="sponsorship?.title || 'Élmények kiemelt szponzor logó'"
        loading="lazy"
      />
    </div>
  }
</ng-template>

<ng-template #basicLink let-item="item">
  <a class="header-bottom-menu-list-item-link" [target]="item.target ?? '_self'" [routerLink]="item.link ?? ''">{{ item.title }}</a>
</ng-template>

<ng-template #tagLink let-item="item">
  <a class="header-bottom-menu-list-item-link tag" [target]="item.target ?? '_self'" [routerLink]="item.link ?? ''">
    {{ item.title }}
  </a>
</ng-template>

<ng-template #mobileLink let-menuItem="menuItem">
  <a class="mobile-menu-list-item-link" (click)="closeMobileMenu()" [routerLink]="menuItem.link" [target]="menuItem.target ?? '_self'">{{ menuItem.title }}</a>
</ng-template>

<ng-template #searchResults let-liveSearchResults="liveSearchResults">
  <div *ngIf="searchInput.value.length > 0 && liveSearchResults.data as results" class="live-search" (clickOutside)="clearSearch()">
    @if (results.length > 0) {
      <ul>
        <li *ngFor="let result of results" class="live-search-result">
          <a [routerLink]="['elmenyek', 'esemenyek', result.occasionSlug]" (click)="clearSearch()" class="live-search-result-text">
            <span class="title">{{ result.experienceTitle }}</span>
            <span class="date">
              @if (result?.occasionStartOfExperienceEvent) {
                {{ result?.occasionStartOfExperienceEvent | formatDate: 'y-l-m-d-h-m' }}
              } @else {
                Hamarosan
              }
            </span>
          </a>
        </li>
      </ul>
    }
  </div>
</ng-template>
