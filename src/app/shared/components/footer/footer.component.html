<footer>
  <div class="footer-container banner-container">
    <img class="logo" width="220" height="59" src="/assets/images/mindmegette-logo.svg" alt="Mindmegette" loading="lazy" (click)="onLogoClick()" />
    <div class="social-icons-container">
      <a *ngFor="let icon of icons" [href]="icon.url" target="_blank">
        <img [src]="icon.src" loading="lazy" [alt]="icon.title" />
      </a>
    </div>
    <button class="btn btn-primary-white btn-large" (click)="onSubscribe()">
      <span>Feliratkozom a hírlevélre</span>
      <span class="icon icon-right"></span>
    </button>
  </div>
  <div class="footer-container top-menu-container" *ngIf="footerMenuTop?.length">
    <div *ngFor="let menuItem of footerMenuTop" class="menu-item">
      <div class="category" (click)="menuItem.isOpen = !menuItem.isOpen">
        <span>{{ menuItem.title }}</span>
        <img
          *ngIf="menuItem.hasSubItems"
          [src]="menuItem.isOpen ? '/assets/images/dropdown-arrow-up.svg' : '/assets/images/dropdown-arrow-down.svg'"
          alt=""
          loading="lazy"
        />
      </div>
      <div class="item-list" [class.opened]="menuItem.isOpen">
        <ng-container *ngFor="let menuItemChild of menuItem.children">
          <ng-container *ngTemplateOutlet="menuElement; context: { item: menuItemChild }"></ng-container>
        </ng-container>
      </div>
    </div>
  </div>
  <div class="footer-container bottom-menu-container" *ngIf="footerMenuBottom?.length">
    <ng-container *ngFor="let menuItem of footerMenuBottom; let last = last">
      <ng-container *ngTemplateOutlet="menuElement; context: { item: menuItem }"></ng-container>
      <span class="separator-vertical" *ngIf="!last || (fixMenus?.length || 0) > 0">|</span>
    </ng-container>
    <ng-container *ngFor="let menuItem of fixMenus; let last = last">
      <button class="footer-menu-link" (click)="menuItem.action()">{{ menuItem.title }}</button>
      <span class="separator-vertical" *ngIf="!last">|</span>
    </ng-container>
  </div>
</footer>

<ng-template #menuElement let-item="item">
  <ng-container *ngTemplateOutlet="!item.isCustomUrl ? normalLink : customUrl; context: { item: item }"></ng-container>
</ng-template>
<ng-template #normalLink let-item="item">
  <a class="footer-menu-link" [routerLink]="item.link" [target]="item.target"> {{ item.title }}</a>
</ng-template>
<ng-template #customUrl let-item="item">
  <a class="footer-menu-link" [href]="item.link" [target]="item.target"> {{ item.title }}</a>
</ng-template>
