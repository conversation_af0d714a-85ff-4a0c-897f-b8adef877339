import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { FormSelectComponent, KesmaFormControlComponent } from '@trendency/kesma-ui';
import { MindmegetteSimpleButtonComponent } from '../simple-button/simple-button.component';
import { NgSelectModule } from '@ng-select/ng-select';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgIf } from '@angular/common';

@Component({
  selector: 'mindmegette-form-select',
  templateUrl: './form-select.component.html',
  styleUrls: ['./form-select.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, FormsModule, ReactiveFormsModule, KesmaFormControlComponent, NgSelectModule, MindmegetteSimpleButtonComponent],
})
export class MindmegetteFormSelectComponent extends FormSelectComponent {
  @Input() isRequired = false;
  @Input() isOccasionSearchPage = false;
  @Input() ariaLabel: string;
}
