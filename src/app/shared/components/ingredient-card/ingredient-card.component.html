<a [routerLink]="ingredientLink">
  <div class="ingredient-card" [class.has-background]="hasBackground">
    <ng-container *ngTemplateOutlet="IngredientCardThumbnail"></ng-container>
    <h2 class="title">{{ data?.title }}</h2>
    <div class="recipe-count" *ngIf="data?.usageCount as usageCount">{{ usageCount }} recept</div>
  </div>
</a>

<ng-template #IngredientCardThumbnail>
  <div class="article-card-wrapper">
    <img [alt]="data?.description ?? ''" [src]="data?.thumbnailUrl || 'assets/images/mme-placeholder.jpg'" class="image" loading="lazy" />
  </div>
</ng-template>
