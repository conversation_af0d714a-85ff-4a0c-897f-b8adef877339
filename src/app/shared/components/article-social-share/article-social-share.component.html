<ng-container *ngIf="!showOnlyUrlCopy">
  <div class="item-wrapper">
    <a [href]="getShareUrl('facebook')" target="_blank" aria-label="Megosztás Facebookon" clickStopPropagation>
      <i class="icon mindmegette-icon-facebook"></i>
    </a>
  </div>

  <div class="item-wrapper">
    <a [href]="getShareUrl('messenger')" target="_blank" aria-label="Megosztás Messengeren" clickStopPropagation>
      <i class="icon mindmegette-icon-messenger"></i>
    </a>
  </div>

  <div class="item-wrapper">
    <a [href]="getShareUrl('pinterest')" target="_blank" aria-label="Megosztás Pinteresten" clickStopPropagation>
      <i class="icon mindmegette-icon-pinterest"></i>
    </a>
  </div>

  <div class="item-wrapper">
    <a [href]="getShareUrl('email')" target="_blank" aria-label="Megosztás Emailen" clickStopPropagation>
      <i class="icon mindmegette-icon-mail"></i>
    </a>
  </div>
</ng-container>

<div clickStopPropagation>
  <div class="item-wrapper" [copyToClipboard]="getShareUrl()" (click)="showCopyConfirmation()">
    <i class="icon mindmegette-icon-copy"></i>
  </div>
</div>

<div class="copy-container" *ngIf="urlCopied$ | async as isSlided" [class.slided]="isSlided">
  <span class="copy-container-message">Vágólapra másolva!</span>
</div>
