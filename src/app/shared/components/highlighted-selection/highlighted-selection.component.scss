@use 'shared' as *;

:host {
  display: block;

  .wrapper {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr;
    grid-column-gap: 32px;
    @include media-breakpoint-down(md) {
      display: flex;
      flex-direction: column;
      gap: 32px;
    }
    .left {
      position: relative;

      .cover-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        aspect-ratio: 4/3;
        border-radius: 8px;

        @include media-breakpoint-up(md) {
          min-height: 515px;
        }
      }

      .selections {
        color: var(--kui-gray-950);
        display: flex;
        gap: 14px;
        align-items: center;
        position: absolute;
        background-color: var(--kui-bg);
        bottom: 0;
        left: 0;
        font-family: var(--kui-font-primary);
        font-size: 34px;
        font-style: normal;
        font-weight: 600;
        line-height: 40px;
        letter-spacing: -0.17px;
        padding: 16px 24px 0px 0px;
        text-align: center;
        border-top-right-radius: 8px;

        i {
          height: 32px;
          width: 32px;
        }

        @include media-breakpoint-down(md) {
          padding: 8px 16px 0px 16px;
          border-top-right-radius: 6px;
          font-size: 20px;
          line-height: 26px;
          gap: 8px;
          i {
            height: 24px;
            width: 24px;
          }
        }
      }
    }

    .right {
      display: flex;
      align-items: center;
      @include media-breakpoint-down(md) {
        display: flex;
        padding: 0px 16px;
        flex-direction: column;
        align-items: flex-start;
        align-self: stretch;
      }

      .data {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 0 40px;
        @include media-breakpoint-down(md) {
          padding: 0;
          gap: 16px;
          align-items: flex-start;
        }

        &-title {
          font-family: var(--kui-font-primary);
          color: var(--kui-gray-950);
          font-size: 40px;
          font-style: normal;
          font-weight: 600;
          line-height: 48px;
          letter-spacing: -0.4px;
          text-align: center;
          @include media-breakpoint-down(md) {
            font-size: 24px;
            font-style: normal;
            font-weight: 600;
            line-height: 28px;
            letter-spacing: -0.12px;
            text-align: left;
            margin-top: 0;
          }
        }

        &-details {
          margin-top: 20px;
          display: flex;
          justify-content: center;
          align-items: center;
          height: 20px;
          overflow: hidden;
          @include media-breakpoint-down(md) {
            margin-top: 0;
            :first-child {
              padding-left: 0;
            }
          }
        }

        &-meta {
          padding: 12px;
          color: var(--kui-gray-950);
          font-family: var(--kui-font-secondary);
          font-size: 14px;
          font-weight: 400;
          line-height: 20px;

          &.divider {
            border-right: 1px solid var(--kui-gray-300);
          }
        }

        &-description {
          margin-top: 20px;
          font-family: var(--kui-font-secondary);
          color: var(--kui-gray-950);
          font-size: 18px;
          font-style: normal;
          font-weight: 400;
          line-height: 28px;
          text-align: center;
          @include media-breakpoint-down(md) {
            text-align: left;
            margin-top: 0;
            font-size: 16px;
            line-height: 24px;
          }
        }
      }
    }

    .selection-button {
      display: flex;
      padding: 10px 20px;
      justify-content: center;
      align-items: center;
      gap: 6px;
      margin-top: 40px;
      border-radius: 8px;
      font-family: var(--kui-font-secondary);
      font-size: 16px;
      font-style: normal;
      font-weight: 500;
      line-height: 24px;
      letter-spacing: 0.16px;
      color: var(--kui-white);
      background-color: var(--kui-green-700);
      cursor: pointer;
      transition: background-color ease-out 200ms;
      @include media-breakpoint-down(md) {
        padding: 8px 16px;
        font-size: 14px;
        margin-top: 16px;
        width: 100%;
      }

      &:hover {
        background-color: var(--kui-green-800);
      }

      i {
        width: 20px;
        height: 20px;
      }
    }
  }
}
