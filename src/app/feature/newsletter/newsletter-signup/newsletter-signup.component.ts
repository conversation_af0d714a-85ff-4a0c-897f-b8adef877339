import { ChangeDetectionStrategy, Component, Inject, inject, OnInit, Renderer2 } from '@angular/core';
import { DOCUMENT, NgTemplateOutlet } from '@angular/common';
import { BypassPipe, IMetaData, SeoService, UtilService } from '@trendency/kesma-core';
import { createMMETitle, defaultMetaInfo, LatestRecipesRecommendationComponent, StickyImageWrapperComponent } from '../../../shared';
import { createCanonicalUrlForPageablePage } from '@trendency/kesma-ui';

@Component({
  selector: 'app-newsletter-signup',
  templateUrl: './newsletter-signup.component.html',
  styleUrls: ['./newsletter-signup.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [StickyImageWrapperComponent, NgTemplateOutlet, LatestRecipesRecommendationComponent, BypassPipe],
})
export class NewsletterSignupComponent implements OnInit {
  recaptcha = 'https://www.google.com/recaptcha/api.js';
  action = 'https://api.automizy.com/v2/forms/submit/MeY6cxOUTm-p50ocXILgy6nhOMVDoE2oT5RFsswLqII/e3gpvsjjl6dbtrel4arnz3yex3k';

  readonly #utilService: UtilService = inject(UtilService);

  isGastro: boolean = false;
  prizeDrawStartDate = new Date('2025-03-17T10:00:00').getTime();
  prizeDrawEndDate = new Date('2025-04-13T23:59:59').getTime();

  constructor(
    private readonly seo: SeoService,
    private readonly renderer2: Renderer2,
    @Inject(DOCUMENT) private readonly document: Document
  ) {}

  get isActivePeriod(): boolean {
    const actualDate = new Date().getTime();
    return actualDate >= this.prizeDrawStartDate && actualDate <= this.prizeDrawEndDate;
  }

  ngOnInit(): void {
    if (this.#utilService.isBrowser()) {
      this.isGastro = this.document.defaultView?.history.state.isGastro;
      this.renderRecaptcha(); // Load only in browser to avoid multiple load (SSR issue)
    }

    this.setMetaData();
  }

  private renderRecaptcha(): void {
    const s = this.renderer2.createElement('script');
    s.type = 'text/javascript';
    s.src = this.recaptcha;
    s.text = '';
    this.renderer2.appendChild(this.document.body, s);
  }

  private setMetaData(): void {
    const title = createMMETitle('Hírlevél-feliratkozás');
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
    };
    this.seo.setMetaData(metaData);
    const canonical = createCanonicalUrlForPageablePage('hirlevel-feliratkozas');
    if (canonical) {
      this.seo.updateCanonicalUrl(canonical);
    }
  }
}
