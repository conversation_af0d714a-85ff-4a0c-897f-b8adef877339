<section>
  <app-breadcrumb [data]="[{ label: recipeCategoryTitle ?? '' }]" />
  <div class="wrapper with-aside">
    <div class="left-column">
      <mindmegette-block-title
        [data]="{ text: recipeCategoryTitle | titleWithPage: limitables?.pageCurrent : true }"
        [headingLevel]="1"
        [styleID]="MindmegetteBlockTitleType.PageTitle"
      >
      </mindmegette-block-title>

      <div class="results">
        <div class="results-label">
          Összes recept <sup>({{ limitables?.rowAllCount }} db)</sup>
        </div>
        <div class="sorter">
          <app-search-result-sorter
            (changeValue)="onChangeSort($event)"
            [hideSelects]="['contentType']"
            searchBackendKey="publishDate_order[]"
          ></app-search-result-sorter>
        </div>
      </div>

      <div class="recipes">
        <mindmegette-recipe-card
          *ngFor="let recipe of recipes | slice: 0 : 6; index as i"
          [data]="$any(recipe) | overwritePriorityContentFlags: i"
          [hasBackground]="true"
          [styleID]="RecipeCardType.TopImageLeftAlignedCard"
        >
        </mindmegette-recipe-card>
      </div>

      <!-- AD -->
      <ng-container *ngIf="recipes?.length && recipes.length >= 4">
        <kesma-advertisement-adocean
          *ngIf="adverts?.desktop?.['roadblock_1'] as ad"
          [ad]="ad"
          [style]="{
            margin: 'var(--ad-margin)',
          }"
        ></kesma-advertisement-adocean>

        <kesma-advertisement-adocean
          *ngIf="adverts?.mobile?.['mobilrectangle_1'] as ad"
          [ad]="ad"
          [style]="{
            margin: 'var(--ad-margin)',
          }"
        ></kesma-advertisement-adocean>
      </ng-container>
      <!-- END AD -->

      <div *ngIf="recipes?.length && recipes.length > 6" class="recipes">
        <mindmegette-recipe-card
          *ngFor="let recipe of recipes | slice: 6 : 12; index as i"
          [data]="$any(recipe) | overwritePriorityContentFlags: i + 6"
          [hasBackground]="true"
          [styleID]="RecipeCardType.TopImageLeftAlignedCard"
        >
        </mindmegette-recipe-card>
      </div>

      <!-- AD -->
      <ng-container *ngIf="recipes?.length && recipes.length >= 10">
        <kesma-advertisement-adocean
          *ngIf="adverts?.desktop?.['roadblock_2'] as ad"
          [ad]="ad"
          [style]="{
            margin: 'var(--ad-margin)',
          }"
        ></kesma-advertisement-adocean>

        <kesma-advertisement-adocean
          *ngIf="adverts?.mobile?.['mobilrectangle_2'] as ad"
          [ad]="ad"
          [style]="{
            margin: 'var(--ad-margin)',
          }"
        ></kesma-advertisement-adocean>
      </ng-container>
      <!-- END AD -->

      <div *ngIf="recipes?.length && recipes.length > 12" class="recipes">
        <mindmegette-recipe-card
          *ngFor="let recipe of recipes | slice: 12"
          [data]="$any(recipe)"
          [hasBackground]="true"
          [styleID]="RecipeCardType.TopImageLeftAlignedCard"
        >
        </mindmegette-recipe-card>
      </div>

      <mindmegette-pager
        *ngIf="limitables && limitables.pageMax! > 0"
        [allowAutoScrollToTop]="true"
        [hasFirstLastButton]="false"
        [hasSkipButton]="true"
        [isCountPager]="false"
        [isListPager]="true"
        [maxDisplayedPages]="pagerMaxDisplayedPages"
        [rowAllCount]="limitables.rowAllCount!"
        [rowOnPageCount]="limitables.rowOnPageCount!"
      >
      </mindmegette-pager>

      <app-external-recommendations></app-external-recommendations>
    </div>
    <aside>
      <app-sidebar [adPageType]="adPageType"></app-sidebar>
    </aside>
  </div>
</section>
