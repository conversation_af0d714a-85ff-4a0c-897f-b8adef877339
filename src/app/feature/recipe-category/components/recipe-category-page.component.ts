import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { IMetaData, SchemaOrgService, SeoService } from '@trendency/kesma-core';
import {
  AdvertisementAdoceanComponent,
  AdvertisementAdoceanStoreService,
  createCanonicalUrlForPageablePage,
  LimitableMeta,
  PAGE_TYPES,
  RecipeCard,
  toBool,
} from '@trendency/kesma-ui';
import { AdvertisementsByMedium, AdvertisementVariablesByMediums } from '@trendency/kesma-ui/lib/definitions';
import { ApiResponseMetaList } from '@trendency/kesma-ui/lib/definitions/api-result';
import { Subject } from 'rxjs';
import { map, takeUntil, tap } from 'rxjs/operators';
import { environment } from '../../../../environments/environment';
import {
  AdvertisementMetaService,
  BackendRecipeSearchResponse,
  BlockTitleComponent,
  capitalize,
  ExternalRecommendationsComponent,
  getStructuredDataForRecipeList,
  MindmegetteArticleCardType,
  MindmegetteBlockTitleType,
  MindmegettePagerComponent,
  OverwritePriorityContentFlagsPipe,
  RecipeCardComponent,
  RecipeCardType,
  recipeCategoryMetaInfo,
  SearchResultSorterComponent,
  TitleWithPagePipe,
} from '../../../shared';
import { SidebarComponent } from '../../layout/components/sidebar/sidebar.component';
import { NgFor, NgIf, SlicePipe } from '@angular/common';
import { BreadcrumbComponent } from '@shared/components/breadcrumb/breadcrumb.component';

export const MOBILE_BREAKPOINT = '(max-width: 576px)';

@Component({
  selector: 'app-recipe-category-page',
  templateUrl: './recipe-category-page.component.html',
  styleUrls: ['./recipe-category-page.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NgIf,
    SearchResultSorterComponent,
    NgFor,
    AdvertisementAdoceanComponent,
    ExternalRecommendationsComponent,
    SidebarComponent,
    SlicePipe,
    BlockTitleComponent,
    RecipeCardComponent,
    MindmegettePagerComponent,
    OverwritePriorityContentFlagsPipe,
    BreadcrumbComponent,
    TitleWithPagePipe,
  ],
})
export class RecipeCategoryPageComponent implements OnInit, OnDestroy {
  recipes: RecipeCard[];
  recipeCategoryTitle?: string;
  limitables?: LimitableMeta;
  ads?: Record<string, AdvertisementVariablesByMediums>;
  adPageType = PAGE_TYPES.recipe_pages;
  pagerMaxDisplayedPages = 5;

  readonly MindmegetteBlockTitleType = MindmegetteBlockTitleType;
  readonly MindmegetteArticleCardType = MindmegetteArticleCardType;
  readonly RecipeCardType = RecipeCardType;

  private readonly destroy$: Subject<boolean> = new Subject();

  adverts?: AdvertisementsByMedium;

  isMobile$ = this.breakpointObserver.observe([MOBILE_BREAKPOINT]).pipe(
    tap((state: BreakpointState) => {
      this.pagerMaxDisplayedPages = state?.matches ? 3 : 5;
    })
  );

  constructor(
    private readonly router: Router,
    private readonly route: ActivatedRoute,
    private readonly cdr: ChangeDetectorRef,
    private readonly adStore: AdvertisementAdoceanStoreService,
    private readonly seo: SeoService,
    private readonly schemaService: SchemaOrgService,
    private readonly breakpointObserver: BreakpointObserver,
    private readonly adoMetaService: AdvertisementMetaService
  ) {}

  ngOnInit(): void {
    this.initAds();
    this.route.data
      .pipe(
        map(({ data }) => ({ data })),
        takeUntil(this.destroy$)
      )
      .subscribe(({ data: { data, meta } }) => {
        this.recipeCategoryTitle = meta?.title;
        this.recipes = this.mapBackendRecipeToRecipe(data);
        this.limitables = (meta as ApiResponseMetaList)?.limitable;

        this.schemaService.removeStructuredData();
        if (this.recipes?.length) {
          this.schemaService.insertSchema(getStructuredDataForRecipeList(this.recipes, environment?.siteUrl ?? ''));
        }

        // Clear currently meta keys.
        const advertMeta = this.adStore.advertMeta$.getValue();
        this.adStore.advertMeta$.next({
          vars: advertMeta.vars,
          keys: '',
        });

        const { recipeCategorySlug } = this.route.snapshot.params;
        if (recipeCategorySlug) {
          this.adoMetaService.set([recipeCategorySlug]);
        }

        this.setMetaData();
        this.cdr.markForCheck();
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next(true);
    this.destroy$.complete();
    this.adStore.setArticleParentCategory('');
  }

  onChangeSort(sort: Record<string, string>): void {
    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: { ...sort, page: null },
      queryParamsHandling: 'merge',
    });
  }

  private setMetaData(): void {
    const page = (this.limitables?.pageCurrent || 0) + 1;
    const metaData: IMetaData = recipeCategoryMetaInfo(capitalize(this.recipeCategoryTitle ?? ''), page);
    this.seo.setMetaData(metaData);
    const canonical = createCanonicalUrlForPageablePage('receptkategoria', this.route.snapshot);
    if (canonical) this.seo.updateCanonicalUrl(canonical);
  }

  protected initAds(): void {
    this.resetAds();
    this.adStore.setArticleParentCategory(this.adPageType);

    this.adStore.advertisemenets$.pipe(takeUntil(this.destroy$)).subscribe((ads) => {
      this.adverts = this.adStore.separateAdsByMedium(ads, this.adPageType);
      this.cdr.detectChanges();
    });
  }

  private resetAds(): void {
    this.adverts = undefined;
    this.cdr.detectChanges();
  }

  private mapBackendRecipeToRecipe(recipes: BackendRecipeSearchResponse[]): RecipeCard[] {
    return recipes?.map(
      (recipe) =>
        ({
          ...recipe,
          guarantee: toBool(recipe.isMmeWarranty ?? '0'),
          hasVideo: toBool(recipe.hasVideo ?? '0'),
          author: {
            name: recipe?.publicAuthor?.fullName as string,
            avatarUrl: recipe?.publicAuthor?.avatarFullSizeUrl,
            verified: recipe?.publicAuthor?.isMaestroAuthor || false,
          },
          thumbnail: {
            url: recipe?.coverImage as string,
          },
        }) as RecipeCard
    );
  }
}
