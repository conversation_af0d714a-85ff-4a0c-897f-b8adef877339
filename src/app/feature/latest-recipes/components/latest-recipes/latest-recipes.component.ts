import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>lice<PERSON>ip<PERSON> } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { isDesktopAdShared } from '@shared/utils/advert.utils';
import { IMetaData, SchemaOrgService, SeoService, UtilService } from '@trendency/kesma-core';
import {
  AdvertisementAdoceanComponent,
  AdvertisementAdoceanStoreService,
  createCanonicalUrlForPageablePage,
  LimitableMeta,
  PAGE_TYPES,
  RecipeCard,
} from '@trendency/kesma-ui';
import { Advertisement, AdvertisementsByMedium } from '@trendency/kesma-ui/lib/definitions';
import { Observable, Subject } from 'rxjs';
import { map, takeUntil, tap } from 'rxjs/operators';
import { environment } from '../../../../../environments/environment';
import {
  createMMETitle,
  defaultMetaInfo,
  ExternalRecommendationsComponent,
  getStructuredDataForRecipeList,
  MindmegetteArticleCardType,
  MindmegettePagerComponent,
  RecipeCardComponent,
  RecipeCardType,
  TitleWithPagePipe,
} from '../../../../shared';
import { SidebarComponent } from '../../../layout/components/sidebar/sidebar.component';

@Component({
  selector: 'app-latest-recipes',
  templateUrl: './latest-recipes.component.html',
  styleUrl: './latest-recipes.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NgIf,
    NgFor,
    AdvertisementAdoceanComponent,
    ExternalRecommendationsComponent,
    SidebarComponent,
    AsyncPipe,
    SlicePipe,
    RecipeCardComponent,
    MindmegettePagerComponent,
    TitleWithPagePipe,
  ],
})
export class LatestRecipesComponent implements OnInit, OnDestroy {
  readonly ArticleCardType = MindmegetteArticleCardType;
  readonly RecipeCardType = RecipeCardType;
  recipes$: Observable<RecipeCard[]> = this.route.data.pipe(
    map((res) => res?.['data']?.recipes),
    tap((recipes) => {
      this.schemaService.removeStructuredData();

      if (recipes?.length) {
        this.schemaService.insertSchema(getStructuredDataForRecipeList(recipes, environment?.siteUrl ?? ''));
      }
    })
  );
  limitables$: Observable<LimitableMeta> = this.route.data.pipe(
    map((res) => res['data'].limitable),
    tap(({ pageCurrent }) => {
      if (pageCurrent) {
        pageCurrent = pageCurrent + 1;
      }
      this.setMetaData(pageCurrent);
    })
  );
  adverts?: AdvertisementsByMedium;
  adPageType = PAGE_TYPES.recipe_pages;
  box_1_tablet?: Advertisement;
  box_2_tablet?: Advertisement;
  box_3_tablet?: Advertisement;
  box_4_tablet?: Advertisement;
  showDesktopAd: boolean;
  page = 0;
  private readonly unsubscribe$: Subject<boolean> = new Subject();

  constructor(
    private readonly cdr: ChangeDetectorRef,
    private readonly adStore: AdvertisementAdoceanStoreService,
    private readonly route: ActivatedRoute,
    private readonly seo: SeoService,
    private readonly schemaService: SchemaOrgService,
    private readonly utilsService: UtilService
  ) {}

  ngOnInit(): void {
    this.initAds();
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next(true);
    this.unsubscribe$.complete();
    this.adStore.setArticleParentCategory(this.adPageType);
  }

  protected initAds(): void {
    this.resetAds();
    this.adStore.setArticleParentCategory(this.adPageType);

    this.adStore.advertisemenets$.pipe(takeUntil(this.unsubscribe$)).subscribe((ads) => {
      this.showDesktopAd = isDesktopAdShared(this.utilsService.isBrowser(), 1030);
      this.adverts = this.adStore.separateAdsByMedium(ads, this.adPageType);

      this.box_1_tablet = { ...this.adverts?.mobile?.['mobilrectangle_1'], medium: 'desktop' };
      this.box_2_tablet = { ...this.adverts?.mobile?.['mobilrectangle_2'], medium: 'desktop' };
      this.box_3_tablet = {
        ...this.adverts?.mobile?.['mobilrectangle_3'],
        medium: 'desktop',
      };
      this.box_4_tablet = { ...this.adverts?.mobile?.['mobilrectangle_4'], medium: 'desktop' };

      this.cdr.detectChanges();
    });
  }

  private resetAds(): void {
    this.adverts = undefined;
    this.cdr.detectChanges();
  }

  private setMetaData(page?: number): void {
    const pagePrefix = page === undefined ? '' : `${page}. oldal - `;
    const title = pagePrefix + createMMETitle('Legfrissebb receptek');
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
    };
    this.seo.setMetaData(metaData);
    const canonical = createCanonicalUrlForPageablePage('legfrissebb-receptek', this.route.snapshot);
    if (canonical) {
      this.seo.updateCanonicalUrl(canonical);
    }
  }
}
