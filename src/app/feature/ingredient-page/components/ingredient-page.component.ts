import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, SlicePipe } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { isDesktopAdShared } from '@shared/utils/advert.utils';
import { IMetaData, SchemaOrgService, SeoService, UtilService } from '@trendency/kesma-core';
import {
  Advertisement,
  AdvertisementAdoceanComponent,
  AdvertisementAdoceanStoreService,
  AdvertisementsByMedium,
  ApiResponseMetaList,
  ArticleSeoFields,
  createCanonicalUrlForPageablePage,
  LimitableMeta,
  PAGE_TYPES,
  RecipeCard,
  toBool,
} from '@trendency/kesma-ui';
import { Subject, takeUntil } from 'rxjs';
import { environment } from '../../../../environments/environment';
import {
  capitalize,
  createKeywords,
  defaultMetaInfo,
  getStructuredDataForRecipeList,
  Ingredient,
  MindmegettePagerComponent,
  MindmegetteWysiwygBoxComponent,
  MMEWysiwygBoxTypes,
  RecipeCardComponent,
  RecipeCardType,
  RecipeEnergyContentComponent,
  SearchResultSorterComponent,
  TitleWithPagePipe,
} from '../../../shared';
import { SidebarComponent } from '../../layout/components/sidebar/sidebar.component';
import { Recipe } from '../../recipe-page/recipe-page.definitions';
import { BreadcrumbComponent } from '@shared/components/breadcrumb/breadcrumb.component';

@Component({
  selector: 'app-ingredient-page',
  templateUrl: 'ingredient-page.component.html',
  styleUrls: ['ingredient-page.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NgIf,
    RecipeEnergyContentComponent,
    AdvertisementAdoceanComponent,
    SearchResultSorterComponent,
    NgFor,
    SidebarComponent,
    SlicePipe,
    MindmegetteWysiwygBoxComponent,
    RecipeCardComponent,
    MindmegettePagerComponent,
    BreadcrumbComponent,
    TitleWithPagePipe,
  ],
})
export class IngredientPageComponent implements OnInit, OnDestroy {
  ingredient?: Ingredient & { seo?: ArticleSeoFields };
  recipes: RecipeCard[] = [];
  limitables?: LimitableMeta;
  adverts?: AdvertisementsByMedium;
  readonly unsubscribe$: Subject<void> = new Subject();

  box_2_tablet?: Advertisement;
  box_3_tablet?: Advertisement;
  showDesktopAd: boolean;

  readonly RecipeCardType = RecipeCardType;
  readonly MMEWysiwygBoxTypes = MMEWysiwygBoxTypes;

  constructor(
    private readonly route: ActivatedRoute,
    private readonly cdr: ChangeDetectorRef,
    private readonly seo: SeoService,
    private readonly router: Router,
    private readonly adStore: AdvertisementAdoceanStoreService,
    private readonly schemaService: SchemaOrgService,
    private readonly utilsService: UtilService
  ) {}

  ngOnInit(): void {
    this.route.data.pipe(takeUntil(this.unsubscribe$)).subscribe(({ data: { recipes, ingredient } }) => {
      this.ingredient = ingredient?.data;
      this.recipes = recipes.data.map((recipe: any) => {
        return {
          ...recipe,
          hasVideo: toBool(recipe.hasVideo ?? '0'),
          thumbnail: {
            url: recipe.coverImage,
          },
          author: {
            name: recipe?.publicAuthor?.fullName,
            id: recipe?.publicAuthor?.id,
            verified: recipe?.publicAuthor?.isMaestroAuthor,
            avatarUrl: recipe?.publicAuthor?.avatar?.thumbnailUrl,
            slug: recipe?.publicAuthor?.slug,
          },
        } as RecipeCard;
      });
      this.limitables = (recipes?.meta as ApiResponseMetaList)?.limitable;

      this.schemaService.removeStructuredData();
      if (this.recipes?.length) {
        this.schemaService.insertSchema(getStructuredDataForRecipeList(this.recipes, environment?.siteUrl ?? ''));
      }

      this.setMetaData();
      this.initAds();
      this.cdr.markForCheck();
    });
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }

  public ingredientAsRecipe(): Recipe {
    return this.ingredient as Recipe;
  }

  onChangeSort(sort: Record<string, string>): void {
    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: { ...sort, page: null },
      queryParamsHandling: 'merge',
    });
  }

  protected initAds(): void {
    this.resetAds();

    this.adStore.advertisemenets$.pipe(takeUntil(this.unsubscribe$)).subscribe((ads) => {
      this.showDesktopAd = isDesktopAdShared(this.utilsService.isBrowser(), 1030);
      this.adverts = this.adStore.separateAdsByMedium(ads, PAGE_TYPES.recipe_pages);

      this.box_2_tablet = { ...this.adverts?.mobile?.['mobilrectangle_2'], medium: 'desktop' };
      this.box_3_tablet = { ...this.adverts?.mobile?.['mobilrectangle_3'], medium: 'desktop' };
      this.cdr.detectChanges();
    });
  }

  private resetAds(): void {
    this.adverts = undefined;
    this.cdr.detectChanges();
  }

  private setMetaData(): void {
    const capitalizedTitle = capitalize(this.ingredient?.title ?? '');
    const page = (this.limitables?.pageCurrent || 0) + 1;
    const pageTitle = page > 1 ? page + '. oldal - ' : '';
    const defaultTitle = capitalize(`${this.ingredient?.seo?.seoTitle || this.ingredient?.title} | ${defaultMetaInfo.ogSiteName}`);
    const title = pageTitle.concat(defaultTitle);

    //eslint-disable-next-line max-len
    const defaultDescription = `${capitalizedTitle} alapanyagot tartalmazó receptlista és leírás a Mindmegette oldalán. Felhasználási és tárolási javaslatok, alapanyag neve tippek és trükkök a mindennapokban.`;
    const description = this.ingredient?.seo?.seoDescription || defaultDescription;

    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title,
      ogTitle: title,
      ogImage: this.ingredient?.coverImage?.fullSizeUrl,
      description,
      ogDescription: description,
      keywords: createKeywords(this.ingredient?.seo as ArticleSeoFields) || `${capitalizedTitle}, ${capitalizedTitle} recept, ${capitalizedTitle} receptek`,
      robots: this.ingredient?.seo?.seoRobotsMeta || defaultMetaInfo.robots,
    };
    this.seo.setMetaData(metaData);
    const canonical = createCanonicalUrlForPageablePage('hozzavalo', this.route.snapshot);
    if (canonical) this.seo.updateCanonicalUrl(canonical);
  }
}
