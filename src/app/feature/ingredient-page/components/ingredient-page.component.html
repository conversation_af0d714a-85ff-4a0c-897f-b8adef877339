<section>
  <app-breadcrumb [data]="[{ label: 'Hozzávalók', url: '/hozzavalok' }, { label: ingredient?.title ?? '' }]" />
  <div class="wrapper with-aside">
    <main class="left-column">
      <div class="sides">
        <div class="left-side">
          <img
            class="ingredient-thumbnail"
            [src]="ingredient?.coverImage?.thumbnailUrl || '/assets/images/placeholder.jpg'"
            [alt]="ingredient?.title"
            loading="eager"
            fetchpriority="high"
          />
        </div>
        <div class="right-side">
          <h1 class="ingredient-title">{{ 'Receptek ' + ingredient?.title?.toLowerCase() + ' hozzávalóval' | titleWithPage: limitables?.pageCurrent }}</h1>
          @if (!(this.limitables?.pageCurrent || 0)) {
            <h2 class="info-title">Leírás</h2>
            <mindmegette-wysiwyg-box
              *ngIf="ingredient?.description as description"
              [html]="description"
              [boxDisplayType]="MMEWysiwygBoxTypes.IngredientContent"
            ></mindmegette-wysiwyg-box>
            <ng-container *ngIf="ingredient?.whenBuy">
              <h2 class="info-title">Mikor vedd?</h2>
              <div class="when-buy-description">
                {{ ingredient?.whenBuy }}
              </div>
            </ng-container>
            <ng-container *ngIf="ingredient?.whereHold">
              <h2 class="info-title">Hogyan tárold?</h2>
              <div class="where-hold-description">
                {{ ingredient?.whereHold }}
              </div>
            </ng-container>

            <app-recipe-energy-content [data]="ingredientAsRecipe()" [isIngredient]="true"></app-recipe-energy-content>
          }

          <kesma-advertisement-adocean *ngIf="adverts?.mobile?.mobilrectangle_1 as ad" [style]="{ margin: 'var(--ad-margin)' }" [ad]="ad">
          </kesma-advertisement-adocean>
        </div>
      </div>

      <div class="results">
        <div class="results-label">
          {{ ingredient?.title }} receptek <sup>({{ limitables?.rowAllCount || 0 }} db)</sup>
        </div>
        <div class="sorter">
          <app-search-result-sorter searchBackendKey="publishDate_order[]" [hideSelects]="['contentType']" (changeValue)="onChangeSort($event)">
          </app-search-result-sorter>
        </div>
      </div>

      <div class="recipes">
        <mindmegette-recipe-card
          [data]="recipe"
          [hasBackground]="true"
          [styleID]="RecipeCardType.TopImageLeftAlignedCard"
          *ngFor="let recipe of recipes | slice: 0 : 6"
        >
        </mindmegette-recipe-card>
      </div>

      <ng-container *ngIf="recipes?.length && recipes.length >= 4">
        <kesma-advertisement-adocean *ngIf="showDesktopAd && adverts?.desktop?.roadblock_1 as ad" [style]="{ margin: 'var(--ad-margin)' }" [ad]="ad">
        </kesma-advertisement-adocean>

        <kesma-advertisement-adocean *ngIf="!showDesktopAd && box_2_tablet as ad" [style]="{ margin: 'var(--ad-margin)' }" [ad]="ad">
        </kesma-advertisement-adocean>

        <kesma-advertisement-adocean *ngIf="adverts?.mobile?.mobilrectangle_2 as ad" [style]="{ margin: 'var(--ad-margin)' }" [ad]="ad">
        </kesma-advertisement-adocean>
      </ng-container>

      <div class="recipes" *ngIf="recipes?.length && recipes.length > 6">
        <mindmegette-recipe-card
          [data]="recipe"
          [hasBackground]="true"
          [styleID]="RecipeCardType.TopImageLeftAlignedCard"
          *ngFor="let recipe of recipes | slice: 6 : 12"
        >
        </mindmegette-recipe-card>
      </div>

      <ng-container *ngIf="recipes?.length && recipes.length >= 10">
        <kesma-advertisement-adocean *ngIf="showDesktopAd && adverts?.desktop?.roadblock_2 as ad" [style]="{ margin: 'var(--ad-margin)' }" [ad]="ad">
        </kesma-advertisement-adocean>

        <kesma-advertisement-adocean *ngIf="!showDesktopAd && box_3_tablet as ad" [style]="{ margin: 'var(--ad-margin)' }" [ad]="ad">
        </kesma-advertisement-adocean>

        <kesma-advertisement-adocean *ngIf="adverts?.mobile?.mobilrectangle_3 as ad" [style]="{ margin: 'var(--ad-margin)' }" [ad]="ad">
        </kesma-advertisement-adocean>
      </ng-container>

      <div class="recipes" *ngIf="recipes?.length && recipes.length > 12">
        <mindmegette-recipe-card
          [data]="recipe"
          [hasBackground]="true"
          [styleID]="RecipeCardType.TopImageLeftAlignedCard"
          *ngFor="let recipe of recipes | slice: 12"
        >
        </mindmegette-recipe-card>
      </div>

      <mindmegette-pager
        [rowAllCount]="limitables?.rowAllCount!"
        [rowOnPageCount]="limitables?.rowOnPageCount!"
        [isListPager]="true"
        [isCountPager]="false"
        [hasFirstLastButton]="false"
        [hasSkipButton]="true"
        [allowAutoScrollToTop]="true"
        [maxDisplayedPages]="5"
        *ngIf="limitables && limitables.pageMax! > 0"
      >
      </mindmegette-pager>
    </main>
    <aside>
      <app-sidebar></app-sidebar>
    </aside>
  </div>
</section>
