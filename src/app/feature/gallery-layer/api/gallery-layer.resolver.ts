import { Injectable } from '@angular/core';
import { HttpErrorResponse } from '@angular/common/http';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { catchError, Observable, throwError } from 'rxjs';
import { GalleryLayerService } from './gallery-layer.service';

@Injectable({
  providedIn: 'root',
})
export class GalleryLayerResolver {
  constructor(
    private readonly GalleryLayerService: GalleryLayerService,
    private readonly router: Router
  ) {}

  resolve(route: ActivatedRouteSnapshot): Observable<any> {
    const slug: string = route.params['gallerySlug'];
    return this.GalleryLayerService.getGalleryDetails(slug).pipe(
      catchError((error: HttpErrorResponse) => {
        this.router
          .navigate(['/', '404'], {
            skipLocationChange: true,
          })
          .then();
        return throwError(() => error);
      })
    );
  }
}
