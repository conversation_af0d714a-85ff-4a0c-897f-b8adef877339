<section class="tags-page">
  <app-breadcrumb *ngIf="tag$ | async as tag" [data]="[{ label: tag.title ?? '' }]" />
  <div class="wrapper with-aside">
    <main *ngIf="searchResults$ | async as results" class="left-column">
      <h1 *ngIf="tag$ | async as tag" class="tag-title">{{ tag.title | titleWithPage: (limitables$ | async)?.pageCurrent : true }}</h1>
      <div class="results">
        <div *ngIf="limitables$ | async as limitables" class="results-label">
          Összes találat <sup>({{ limitables.rowAllCount }} db)</sup>
        </div>
        <div class="sorter">
          <app-search-result-sorter (changeValue)="onChangeSort($event)"></app-search-result-sorter>
        </div>
      </div>

      <div class="article-card-wrapper">
        <ng-container *ngIf="results | slice: 0 : 6 as slicedResults">
          <ng-container *ngFor="let result of slicedResults; index as i">
            <ng-container *ngIf="result.contentType !== 'recipe' && backendSearchResultToArticleCard(result) as articleData">
              <mindmegette-article-card
                [data]="articleData | overwritePriorityContentFlags: i"
                [hasBackground]="true"
                [styleID]="ArticleCardType.TopImageLeftAlignedCard"
              >
              </mindmegette-article-card>
            </ng-container>
            <ng-container *ngIf="result.contentType === 'recipe' && backendSearchResultToRecipeCard(result) as recipeData">
              <mindmegette-recipe-card
                [data]="recipeData | overwritePriorityContentFlags: i"
                [hasBackground]="true"
                [styleID]="RecipeCardType.TopImageLeftAlignedCard"
              ></mindmegette-recipe-card>
            </ng-container>
          </ng-container>
        </ng-container>
      </div>

      <!-- AD -->
      <ng-container *ngIf="results?.length && results.length >= 4">
        <kesma-advertisement-adocean
          *ngIf="showDesktopAd && adverts?.desktop?.['roadblock_1'] as ad"
          [ad]="ad"
          [style]="{
            margin: 'var(--ad-margin)',
          }"
        ></kesma-advertisement-adocean>

        <kesma-advertisement-adocean *ngIf="!showDesktopAd && box_1_tablet as ad" [ad]="ad" [style]="{ margin: 'var(--ad-margin)' }">
        </kesma-advertisement-adocean>

        <kesma-advertisement-adocean
          *ngIf="adverts?.mobile?.['mobilrectangle_1'] as ad"
          [ad]="ad"
          [style]="{
            margin: 'var(--ad-margin)',
          }"
        ></kesma-advertisement-adocean>
      </ng-container>
      <!-- END AD -->

      <div *ngIf="results?.length && results.length > 6" class="article-card-wrapper">
        <ng-container *ngIf="results | slice: 6 : 12 as slicedResults">
          <ng-container *ngFor="let result of slicedResults; index as i">
            <ng-container *ngIf="result.contentType !== 'recipe' && backendSearchResultToArticleCard(result) as articleData">
              <mindmegette-article-card
                [data]="articleData | overwritePriorityContentFlags: i + 6"
                [hasBackground]="true"
                [styleID]="ArticleCardType.TopImageLeftAlignedCard"
              >
              </mindmegette-article-card>
            </ng-container>
            <ng-container *ngIf="result.contentType === 'recipe' && backendSearchResultToRecipeCard(result) as recipeData">
              <mindmegette-recipe-card
                [data]="recipeData | overwritePriorityContentFlags: i + 6"
                [hasBackground]="true"
                [styleID]="RecipeCardType.TopImageLeftAlignedCard"
              ></mindmegette-recipe-card>
            </ng-container>
          </ng-container>
        </ng-container>
      </div>

      <!-- AD -->
      <ng-container *ngIf="results?.length && results.length >= 10">
        <kesma-advertisement-adocean
          *ngIf="showDesktopAd && adverts?.desktop?.['roadblock_2'] as ad"
          [ad]="ad"
          [style]="{
            margin: 'var(--ad-margin)',
          }"
        ></kesma-advertisement-adocean>

        <kesma-advertisement-adocean *ngIf="!showDesktopAd && box_2_tablet as ad" [ad]="ad" [style]="{ margin: 'var(--ad-margin)' }">
        </kesma-advertisement-adocean>

        <kesma-advertisement-adocean
          *ngIf="adverts?.mobile?.['mobilrectangle_2'] as ad"
          [ad]="ad"
          [style]="{
            margin: 'var(--ad-margin)',
          }"
        ></kesma-advertisement-adocean>
      </ng-container>
      <!-- END AD -->

      <div *ngIf="results?.length && results.length > 12" class="article-card-wrapper">
        <ng-container *ngIf="results | slice: 12 as slicedResults">
          <ng-container *ngFor="let result of slicedResults">
            <ng-container *ngIf="result.contentType !== 'recipe' && backendSearchResultToArticleCard(result) as articleData">
              <mindmegette-article-card [data]="articleData" [hasBackground]="true" [styleID]="ArticleCardType.TopImageLeftAlignedCard">
              </mindmegette-article-card>
            </ng-container>
            <ng-container *ngIf="result.contentType === 'recipe' && backendSearchResultToRecipeCard(result) as recipeData">
              <mindmegette-recipe-card [data]="recipeData" [hasBackground]="true" [styleID]="RecipeCardType.TopImageLeftAlignedCard"></mindmegette-recipe-card>
            </ng-container>
          </ng-container>
        </ng-container>
      </div>

      <ng-container *ngIf="limitables$ | async as limitable">
        <mindmegette-pager
          *ngIf="limitable && limitable.pageMax! > 0"
          [allowAutoScrollToTop]="true"
          [hasFirstLastButton]="false"
          [hasSkipButton]="true"
          [isCountPager]="false"
          [isListPager]="true"
          [maxDisplayedPages]="5"
          [rowAllCount]="limitable.rowAllCount!"
          [rowOnPageCount]="limitable.rowOnPageCount!"
        ></mindmegette-pager>
      </ng-container>

      <app-external-recommendations></app-external-recommendations>
    </main>
    <aside>
      <app-sidebar></app-sidebar>
    </aside>
  </div>
</section>
