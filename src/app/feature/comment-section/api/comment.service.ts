import { inject, Injectable } from '@angular/core';
import { BehaviorSubject, first, Observable, switchMap } from 'rxjs';
import {
  ApiResponseMetaList,
  ApiResult,
  Comment,
  CommentListResponse,
  CommentWithArticle,
  mapBackendCommentWithArticleToCommentWithArticle,
  mapToCommentListResponse,
  Ordering,
  populateReactionsForComments,
  RedirectService as KesmaRedirectService,
  UserCommentsResponse,
} from '@trendency/kesma-ui';
import { map, tap } from 'rxjs/operators';
import { ApiService, AuthService, SecureApiService, UserCommentCount } from '../../../shared';

@Injectable({
  providedIn: 'root',
})
export class CommentService {
  private readonly _dropDownOpened = new BehaviorSubject<string | null>(null);
  /** Emits the id of the comment, where any dropdown is opened. */
  get dropDownOpened(): Observable<string | null> {
    return this._dropDownOpened.asObservable();
  }

  constructor(
    private readonly _api: ApiService,
    private readonly _authService: AuthService,
    private readonly _secureApi: SecureApiService
  ) {}
  private readonly kesmaRedirectService = inject(KesmaRedirectService);

  /**
   * Signals that a dropdown is opened for a comment-card.
   * @remarks Other comment-cards will close their dropdowns if any of them is opened.
   * @param id The id of the comment-card, where the dropdown is opened.
   */
  openDropdownForId(id: string): void {
    this._dropDownOpened.next(id);
  }

  /**
   * Signals that a dropdown is closed for a comment.
   * @remarks At this point no comment-card should have a dropdown opened.
   * @param id
   */
  closeDropdownForId(id: string): void {
    if (this._dropDownOpened.value !== id) {
      return;
    }
    this._dropDownOpened.next(null);
  }

  /**
   * Returns the active user id.
   * @throws Error if no user is logged in.
   */
  get userId(): string {
    const currentUser = this._authService.currentUser;
    if (!currentUser) {
      throw new Error('No user is logged in.');
    }
    return currentUser.uid || '';
  }

  /**
   * Returns the list of comments for the given article.
   * If the user is logged in, this will call the SecureApi instead of the PublicApi.
   * In this case, the reactions of the user will be added to the comments.
   * @param articleId The id of the article, where the comments are.
   * @param pageNumber The page number of the comments.
   * @param perPage The number of comments per page.
   * @param sort The sort order of the comments.
   * @param type Whether the comments are for an article or a recipe.
   */
  getCommentsForArticle(
    articleId: string,
    pageNumber = 0,
    perPage = 20,
    sort: Ordering = 'latest',
    type: 'article' | 'recipe' = 'article'
  ): Observable<CommentListResponse> {
    if (!this._authService.currentUser) {
      return this._api.getCommentListFor(
        articleId,
        sort,
        {
          page_limit: pageNumber,
          rowCount_limit: perPage,
        },
        type
      );
    }

    return this._secureApi.getMyVotesFor(articleId, { ...this.getSorting(sort) }, this.userId, type).pipe(
      switchMap((myVotes) =>
        this._secureApi
          .getCommentsFor(
            articleId,
            {
              ...this.getSorting(sort),
              page_limit: pageNumber,
              rowCount_limit: perPage,
            },
            type
          )
          .pipe(
            mapToCommentListResponse(),
            map((comments) => populateReactionsForComments(myVotes.data, comments))
          )
      )
    );
  }

  getUserAllCommentCount(): Observable<UserCommentCount> {
    return this._secureApi.getUserAllCommentCount().pipe(map(({ data }) => data));
  }

  /**
   * Returns the list of answers for the given comment.
   * If the user is logged in, this will call the SecureApi instead of the PublicApi.
   * In this case, the reactions of the user will be added to the comments.
   * @param commentId The id of the comment, where the answers are.
   * @param pageNumber The page number of the answers.
   * @param perPage The number of answers per page.
   * @param sort
   */
  getAnswersForComment(commentId: string, pageNumber = 0, perPage = 20, sort: Ordering = 'latest'): Observable<CommentListResponse> {
    if (!this._authService.currentUser) {
      return this._api.getCommentListFor(
        commentId,
        sort,
        {
          page_limit: pageNumber,
          rowCount_limit: perPage,
        },
        'comment'
      );
    }

    return this._secureApi.getMyVotesFor(commentId, { ...this.getSorting(sort) }, this.userId, 'comment').pipe(
      switchMap((myVotes) =>
        this._secureApi
          .getCommentsFor(
            commentId,
            {
              ...this.getSorting(sort),
              page_limit: pageNumber,
              rowCount_limit: perPage,
            },
            'comment'
          )
          .pipe(
            mapToCommentListResponse(),
            map((comments) => populateReactionsForComments(myVotes.data, comments))
          )
      )
    );
  }

  /**
   * Submits a comment for the given article.
   * @remarks This will call the SecureApi.
   * @param articleId The id of the article, where the comment is posted.
   * @param text The text of the comment.
   * @param type Whether the comment is for an article or a recipe.
   */
  submitComment(articleId: string, text: string, type: 'article' | 'recipe' = 'article'): Observable<ApiResult<unknown>> {
    return this._secureApi.submitCommentFor(articleId, type, text);
  }

  /**
   * Submits an answer for the given comment.
   * @remarks This will call the SecureApi.
   * @param commentId The id of the comment, where the answer is posted.
   * @param text The text of the answer.
   */
  submitAnswer(commentId: string, text: string): Observable<ApiResult<never>> {
    return this._secureApi.submitCommentFor(commentId, 'comment', text);
  }

  /**
   * Edits the given comment.
   * @remarks This will call the SecureApi.
   * @param commentId The id of the comment, which should be edited.
   * @param text The new text of the comment.
   */
  editComment(commentId: string, text: string): Observable<ApiResult<never>> {
    return this._secureApi.editComment(commentId, text);
  }

  /**
   * Posts a like for the given comment. This will call the SecureApi.
   * This will call the SecureApi.
   * If the user has already liked the comment, this does nothing.
   * If the user has disliked the comment, this will remove the dislike and add a like.
   * @param commentId The id of the comment, which should be liked.
   */
  likeComment(commentId: string): Observable<ApiResult<never>> {
    return this._secureApi.voteComment(commentId, 'like');
  }

  /**
   * Posts a dislike for the given comment.
   * This will call the SecureApi.
   * If the user has already disliked the comment, this does nothing.
   * If the user has liked the comment, this will remove the like and add a dislike.
   * @param commentId The id of the comment, which should be disliked.
   */
  dislikeComment(commentId: string): Observable<ApiResult<never>> {
    return this._secureApi.voteComment(commentId, 'dislike');
  }

  /**
   * Clears the like or dislike for the given comment.
   * @remarks This will call the SecureApi.
   * @param commentId The id of the comment, which should be cleared.
   */
  clearReactionForComment(commentId: string): Observable<ApiResult<never>> {
    return this._secureApi.voteComment(commentId, 'clear-like-dislike');
  }

  /**
   * Reports the given comment.
   * @remarks This will call the SecureApi.
   * @param commentId The id of the comment, which should be reported.
   */
  reportComment(commentId: string): Observable<ApiResult<never>> {
    return this._secureApi.voteComment(commentId, 'report');
  }

  /**
   * Returns the list of the user's comments.
   * @remarks This will call the SecureApi.
   * @param pageNumber The page number of the comments.
   * @param perPage The number of comments per page.
   * @param sort
   */
  getMyComments(pageNumber = 0, perPage = 20, sort: Ordering = 'latest'): Observable<ApiResult<CommentWithArticle[], ApiResponseMetaList>> {
    return this._secureApi
      .getMyComments(
        {
          ...this.getSorting(sort),
          page_limit: pageNumber,
          rowCount_limit: perPage,
        },
        this.userId
      )
      .pipe(
        map((res) => ({
          ...res,
          data: res.data.map((item) => mapBackendCommentWithArticleToCommentWithArticle(item)),
        }))
      );
  }

  /**
   * Returns the list of the user's comments.
   * @remarks This will call the SecureApi.
   * @param pageNumber The page number of the comments.
   * @param perPage The number of comments per page.
   * @param sort
   */
  getMyArticleComments(pageNumber = 0, perPage = 20, sort: Ordering = 'latest'): Observable<UserCommentsResponse> {
    return this._secureApi
      .getMyArticleComments({
        ...this.getSorting(sort),
        page_limit: pageNumber,
        rowCount_limit: perPage,
      })
      .pipe(
        map((response) => {
          return {
            ...response,
            data: [
              ...response.data.map((commentedArticle) => ({
                ...commentedArticle,
                thumbnail: {
                  url: (commentedArticle as any).thumbnail || '',
                },
              })),
            ],
          };
        }),
        tap((res) => {
          if (this.kesmaRedirectService.shouldBeRedirect(pageNumber, res.data)) {
            this.kesmaRedirectService.redirectOldUrl(`profil/kommentek`, false, 302);
          }
        })
      );
  }

  optimisticAdd(text: string, articleId: string, data$: BehaviorSubject<CommentListResponse | undefined>): void {
    if (!data$.value) {
      return;
    }

    data$.next({
      meta: data$.value.meta,
      data: [this.getTempComment(text), ...data$.value.data],
    });

    setTimeout(() => {
      this.getCommentsForArticle(articleId, 0, 1)
        .pipe(first())
        .subscribe((res) => {
          if (!res.data.length) {
            console.error('Backend returned no comments for article', articleId);
            return;
          }
          data$.next({
            meta: res.meta,
            data: [res.data[0], ...(data$.value?.data.slice(1) ?? [])],
          });
        });
    }, 2000);
  }

  getTempComment(text = ''): Comment {
    return {
      id: 'new',
      text,
      author: {
        uid: this._authService.currentUser?.uid ?? '',
        username: this._authService.currentUser?.username ?? 'Anonymous',
        email: this._authService.currentUser?.email ?? '',
      },
      createdAt: new Date(),
      answerCount: 0,
      likeCount: 0,
      dislikeCount: 0,
    };
  }

  /**
   * Returns the sorting parameter used in query params.
   * @param sort
   * @private
   */
  private getSorting(sort: Ordering): {
    'createdAt_order[1]'?: 'desc';
    'likeCount_order[0]'?: 'desc';
    'createdAt_order[0]'?: 'asc' | 'desc';
  } {
    return (
      {
        'most-popular': {
          'likeCount_order[0]': 'desc',
          'createdAt_order[1]': 'desc',
        },
        latest: { 'createdAt_order[0]': 'desc' },
        oldest: { 'createdAt_order[0]': 'asc' },
      } as const
    )[sort];
  }
}
