<div #scrollTarget id="kommentek"></div>
<ng-container *ngIf="paginator.data$ | async as data; else spinner">
  <ng-container *ngIf="state.actual$ | async as actualState">
    <div *ngIf="isGuest$ | async" class="comment-warning">
      A kommentek nem szerkesztett tartalmak, tartalmuk a szerzőjük álláspontját tükrözi. Mielőtt hozzászólna, k<PERSON><PERSON><PERSON><PERSON><PERSON>, olvassa el a
      <a [routerLink]="['/felhasznalasi-feltetelek']">kommentszabályzatot</a>.
    </div>
    <ng-container *ngIf="user$ | async">
      <app-comment-answer
        #form
        (response)="submitAnswer($event, form)"
        *ngIf="actualState"
        [buttonColor]="actualState.buttonColor"
        [buttonText]="actualState.buttonText"
        [isIdle]="actualState.isIdle"
        type="comment"
      ></app-comment-answer>
    </ng-container>
    <div class="answer-submitted">
      <span *ngIf="answerSubmitted" class="answer-submitted-text"> <PERSON><PERSON><PERSON>, hogy meg<PERSON>lenjen a listában a kommented, kérlek frissítsd a kommenteket! </span>
    </div>
    <mindmegette-simple-button (click)="reloadComments()" color="outline">
      <span class="refresh">Kommentek frissítése</span>
    </mindmegette-simple-button>
    <div class="sort">
      <h3>Hozzászólások ({{ allCommentCount() }})</h3>
      <mindmegette-form-select
        ariaLabel="Sorbarendezési beállítások"
        [options]="sortOptions"
        [formGroup]="form"
        controlName="sort"
        bindLabel="label"
        bindValue="key"
      ></mindmegette-form-select>
    </div>

    <section class="comments">
      <div [class.active]="paginator.isLoading$ | async" class="comments-spinner">
        <mindmegette-spinner *ngIf="paginator.isLoading$ | async"></mindmegette-spinner>
      </div>
      <app-comment-card-container
        *ngFor="let comment of data ?? []"
        [data]="comment"
        [sort]="sort"
        (newCommentCount)="getCommentCount()"
      ></app-comment-card-container>
    </section>

    <div *ngIf="isGuest$ | async" class="guest-overlay">
      <div>Jelenleg csak a hozzászólások egy kis részét látja. Hozzászóláshoz és a további kommentek megtekintéséhez lépjen be, vagy regisztráljon!</div>
      <div class="buttons">
        <mindmegette-simple-button (click)="promptLogin('bejelentkezes')" color="primary"> Bejelentkezés </mindmegette-simple-button>
        <mindmegette-simple-button (click)="promptLogin('regisztracio')" color="outline"> Regisztráció </mindmegette-simple-button>
      </div>
    </div>

    <mindmegette-simple-button (click)="loadMore()" *ngIf="(paginator.hasMore$ | async) && (user$ | async)" [disabled]="actualState.isDisabled">
      Továbbiak betöltése
    </mindmegette-simple-button>
  </ng-container>
</ng-container>

<ng-template #spinner>
  <mindmegette-spinner *ngIf="paginator.isLoading$ | async"></mindmegette-spinner>
  <div *ngIf="paginator.hasError$ | async" class="error-card">
    <p>Hiba történt a kommentek betöltése közben.</p>
    <mindmegette-simple-button (click)="reloadComments()" color="secondary">Próbálja újra</mindmegette-simple-button>
  </div>
</ng-template>
