import { AfterViewInit, ChangeDetectionStrategy, Component, ElementRef, EventEmitter, Input, OnDestroy, Output, signal, ViewChild } from '@angular/core';
import { CommentService } from '../../../api/comment.service';
import { combineLatest, EMPTY, first, ReplaySubject, Subject } from 'rxjs';
import { LoadMorePaginator, Ordering } from '@trendency/kesma-ui';
import { AnsweringState } from '../../../utils/answering.state';
import { catchError, map, switchMap, takeUntil } from 'rxjs/operators';
import { Router, RouterLink } from '@angular/router';
import { CommentAnswerComponent } from '../../comment-answer/comment-answer.component';
import { UtilService } from '@trendency/kesma-core';
import { AsyncPipe, NgFor, NgIf, ViewportScroller } from '@angular/common';
import { FormsModule, ReactiveFormsModule, UntypedFormBuilder } from '@angular/forms';
import { CommentCardContainerComponent } from '../../comment-card-container/comment-card-container.component';
import { ApiService, AuthService, MindmegetteFormSelectComponent, MindmegetteSimpleButtonComponent, MindmegetteSpinnerComponent } from '../../../../../shared';

@Component({
  selector: 'app-comment-section',
  templateUrl: './comment-section.component.html',
  styleUrls: ['./comment-section.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NgIf,
    RouterLink,
    CommentAnswerComponent,
    FormsModule,
    ReactiveFormsModule,
    NgFor,
    CommentCardContainerComponent,
    AsyncPipe,
    MindmegetteSimpleButtonComponent,
    MindmegetteFormSelectComponent,
    MindmegetteSpinnerComponent,
  ],
})
export class CommentSectionComponent implements AfterViewInit, OnDestroy {
  @ViewChild('scrollTarget', { static: true }) scrollTarget: ElementRef<HTMLElement> | undefined;
  answerSubmitted = false;
  sort: Ordering = 'latest';
  @Input() type: 'article' | 'recipe' = 'article';
  @Output() commentSubmitted = new EventEmitter<void>();
  allCommentCount = signal(0);
  readonly state = new AnsweringState();
  readonly user$ = this.authService.currentUserSubject.asObservable();
  readonly isGuest$ = this.user$.pipe(map((user) => !user));
  readonly articleId$ = new ReplaySubject<string>(1);
  readonly paginator = new LoadMorePaginator((page) =>
    combineLatest({
      articleId: this.articleId$,
      isGuest: this.isGuest$,
    }).pipe(
      first(),
      switchMap(({ articleId, isGuest }) => this.commentService.getCommentsForArticle(articleId, page, isGuest ? 4 : 20, this.sort, this.type))
    )
  );
  readonly sortOptions = [
    {
      key: 'latest',
      label: 'Legfrissebb',
    },
    {
      key: 'most-popular',
      label: 'Legnépszerűbb',
    },
    {
      key: 'oldest',
      label: 'Legrégebbi',
    },
  ];
  readonly form = this.fb.group({
    sort: ['latest'],
  });

  private readonly destroy$ = new Subject<void>();

  constructor(
    private readonly commentService: CommentService,
    private readonly authService: AuthService,
    private readonly router: Router,
    private readonly utilService: UtilService,
    private readonly scroller: ViewportScroller,
    private readonly fb: UntypedFormBuilder,
    private readonly apiService: ApiService
  ) {}

  @Input() set articleID(value: string) {
    this.articleId$.next(value);
    this.paginator.reset();
  }

  @Input() set commentCount(value: number) {
    this.allCommentCount.set(value);
  }

  ngAfterViewInit(): void {
    this.form.valueChanges.pipe(takeUntil(this.destroy$)).subscribe((value) => {
      this.sort = value.sort;
      this.paginator.reset();
    });

    if (!this.router.url.endsWith('#kommentek') || !this.scrollTarget) {
      return;
    }

    let finish = 0;
    const observer = new IntersectionObserver((entries) =>
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          // Element is visible
          finish = setTimeout(() => observer.disconnect(), 2000) as unknown as number; // Layout shift settled, disconnect
          return;
        }

        // Element is not visible
        setTimeout(() => this.scrollToTarget(), 100); // Layout shifted, follow
        clearTimeout(finish);
      })
    );
    observer.observe(this.scrollTarget.nativeElement);
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  loadMore(): void {
    this.paginator.next();
  }

  submitAnswer(text: string, formComponent: CommentAnswerComponent): void {
    if (!this.state.isIdle) {
      return;
    }

    this.state.changeTo('busy').then();
    this.answerSubmitted = true;
    this.articleId$
      .pipe(
        switchMap((articleId) => this.commentService.submitComment(articleId, text, this.type)),
        first(),
        catchError((err) => {
          console.error('[CommentSectionComponent::onSubmit] Error:', err);
          this.state.changeTo('error', 2000).then();
          return EMPTY;
        })
      )
      .subscribe(() => {
        this.state.changeTo('success', 1000).then();
        formComponent.reset();
        this.getCommentCount();
        this.commentSubmitted.emit();
      });
  }

  onSortChange(): void {
    this.paginator.reset();
  }

  promptLogin(to: 'bejelentkezes' | 'regisztracio'): void {
    this.router.navigate([`/${to}`], { queryParams: { redirect: this.router.url } }).then();
  }

  scrollToTarget(): void {
    if (!this.scrollTarget || !this.utilService.isBrowser()) {
      return;
    }

    this.scroller.scrollToAnchor('kommentek');
  }

  reloadComments(): void {
    this.answerSubmitted = false;
    this.paginator.reset();
  }

  getCommentCount(): void {
    // We need setTimeout, because we are waiting for save new comment
    setTimeout(() => {
      this.reloadComments();
      this.articleId$.pipe(switchMap((articleId) => this.apiService.getSocialDataByType(articleId, this.type))).subscribe((data) => {
        if (data.commentCount) {
          this.allCommentCount.set(data.commentCount);
          return;
        }
        this.allCommentCount.update((value: number) => ++value);
      });
    }, 2000);
  }
}
