import {
  RecipeSubmitIngredient,
  RecipeSubmitIngredientGroup,
  RecipeSubmitOtherTime,
  RecipeSubmitSessionData,
  RecipeSubmitWorkflow,
} from './recipe-submit.definitions';

export function recipeSubmitSessionDataToBackendRequest(data: RecipeSubmitSessionData, recaptchaToken: string): FormData {
  const body: FormData = new FormData();

  body.append('recipeName', `${data.recipeName}`);
  body.append('recaptcha', recaptchaToken);

  let ingredientGroupIndex = 0;
  let ingredientIndex = 0;
  data.ingredientGroups?.forEach((ingredientGroup: RecipeSubmitIngredientGroup): void => {
    if (ingredientGroup?.ingredientGroupName) {
      body.append(`ingredientGroups[${ingredientGroupIndex}][ingredientGroupName]`, `${ingredientGroup?.ingredientGroupName}`);
    }

    ingredientIndex = 0;
    ingredientGroup.ingredients?.forEach((ingredient: RecipeSubmitIngredient): void => {
      if (ingredient.ingredient?.slug) {
        body.append(`ingredientGroups[${ingredientGroupIndex}][ingredients][${ingredientIndex}][ingredient][slug]`, `${ingredient.ingredient?.slug}`);
      }
      body.append(`ingredientGroups[${ingredientGroupIndex}][ingredients][${ingredientIndex}][ingredient][title]`, `${ingredient.ingredient?.title}`);
      body.append(`ingredientGroups[${ingredientGroupIndex}][ingredients][${ingredientIndex}][quantity]`, `${ingredient.quantity}`);
      if (ingredient.unit) {
        body.append(`ingredientGroups[${ingredientGroupIndex}][ingredients][${ingredientIndex}][unit]`, `${ingredient.unit}`);
      }
      ingredientIndex++;
    });

    ingredientGroupIndex++;
  });

  body.append('makingText', `${data.workflow?.map((workflow: RecipeSubmitWorkflow): string => `<p>${workflow.stepText}</p>`).join('')}`);

  body.append('madeForPeople', `${data.portion}`);
  body.append('difficulty', `${data.difficulty?.toLowerCase()}`);
  body.append('cost', `${data.cost?.toLowerCase()}`);

  if (data.comment) {
    body.append('comment', `${data.comment}`);
  }

  body.append('prepareTime', `${data.prepareTime}`);
  body.append('cookTime', `${data.cookTime}`);

  let otherTimeIndex = 0;
  data.otherTimes?.forEach((otherTime: RecipeSubmitOtherTime): void => {
    body.append(`otherTimes[${otherTimeIndex}][timeName]`, `${otherTime.timeName}`);
    body.append(`otherTimes[${otherTimeIndex}][time]`, `${otherTime.time}`);
    otherTimeIndex++;
  });

  body.append('totalTime', `${data.totalTime}`);
  body.append('isNotifiable', data.shouldSendNotification ? '1' : '0');
  body.append('isNationalDish2025', data.isNationalDish2025 ? '1' : '0');

  if ((data.images.length ?? 0) > 0) {
    body.append('coverImage', data.images[0].file);
  }

  if ((data.images.length ?? 0) > 1) {
    body.append('secondaryCoverImage', data.images[1].file);
  }

  return body;
}
