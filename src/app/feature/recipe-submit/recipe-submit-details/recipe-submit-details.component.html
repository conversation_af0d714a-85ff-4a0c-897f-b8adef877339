<div class="recipe-submit">
  <div class="recipe-submit-recipe-name">{{ recipeName }}</div>

  <form (ngSubmit)="navigateToNextStep()" *ngIf="formGroup" [formGroup]="formGroup">
    <div class="mindmegette-form">
      <!-- Adag -->
      <div class="mindmegette-form-row">
        <div class="row">
          <div class="col-12 col-md-6">
            <mindmegette-portion-selector [formGroup]="formGroup" [isRequired]="true" controlName="portion" label="Adag"></mindmegette-portion-selector>
          </div>
        </div>
      </div>

      <!-- Nehézség -->
      <div class="mindmegette-form-row">
        <mindmegette-button-selector
          [formGroup]="formGroup"
          [id]="'difficulty'"
          [isRequired]="true"
          [items]="difficultyOptions"
          controlName="difficulty"
          label="Nehézség"
        ></mindmegette-button-selector>
      </div>

      <!-- Költség -->
      <div class="mindmegette-form-row">
        <mindmegette-button-selector
          [formGroup]="formGroup"
          [id]="'cost'"
          [isRequired]="true"
          [items]="costOptions"
          controlName="cost"
          label="Költség"
        ></mindmegette-button-selector>
      </div>

      <!-- Megjegyzés -->
      <div class="mindmegette-form-row">
        <kesma-form-control>
          <label class="mindmegette-form-label" for="comment">Megjegyzés a recepthez</label>
          <textarea
            class="mindmegette-form-textarea"
            formControlName="comment"
            id="comment"
            placeholder="Írd le a recepthez kapcsolódó tippjeid, megjegyzéseid.."
          ></textarea>
        </kesma-form-control>
      </div>

      <!-- Elkészítési idő -->
      <div class="mindmegette-form-row">
        <div class="row">
          <div class="col-12 col-md-6">
            <mindmegette-form-select
              ariaLabel="Előkészítési idők"
              [clearable]="true"
              [formGroup]="formGroup"
              [id]="'prepareTime'"
              [isRequired]="true"
              [options]="timeUnits"
              [searchable]="true"
              bindLabel="title"
              bindValue="key"
              controlName="prepareTime"
              label="Előkészítési idő"
              placeholder="Válassz"
            ></mindmegette-form-select>
          </div>
        </div>
      </div>

      <!-- Főzési idő -->
      <div class="mindmegette-form-row">
        <div class="row">
          <div class="col-12 col-md-6">
            <mindmegette-form-select
              ariaLabel="Főzési / sütési idők"
              [clearable]="true"
              [formGroup]="formGroup"
              [id]="'cookTime'"
              [isRequired]="true"
              [options]="timeUnits"
              [searchable]="true"
              bindLabel="title"
              bindValue="key"
              controlName="cookTime"
              label="Főzési / sütési idő"
              placeholder="Válassz"
            ></mindmegette-form-select>
          </div>
        </div>
      </div>

      <!-- Egyéb idők -->
      <div class="recipe-submit-form-info-text">
        <i class="icon mindmegette-icon-info"></i>
        <span>Ha szükséges, adj hozzá plusz idő kategóriákat a receptedhez, mint például a kelesztési vagy a sütési idő.</span>
      </div>
      <div class="recipe-submit-other-times">
        <ng-container *ngFor="let otherTime of getOtherTimesFormArray()?.controls; let otherTimeIndex = index">
          <div [formGroup]="$any(otherTime)" class="row recipe-submit-other-time">
            <div class="col-12 mobile-divider">
              <span></span>
            </div>
            <div class="col-12 col-md-6">
              <div class="mindmegette-form-row">
                <kesma-form-control>
                  <label class="mindmegette-form-label" for="timeName{{ otherTimeIndex }}">Idő elnevezése<strong>*</strong></label>
                  <input
                    class="mindmegette-form-input"
                    formControlName="timeName"
                    id="timeName{{ otherTimeIndex }}"
                    placeholder="Pl: Pihentetési idő"
                    type="text"
                  />
                </kesma-form-control>
              </div>
            </div>
            <div class="col-12 col-md-5">
              <div class="mindmegette-form-row">
                <mindmegette-form-select
                  ariaLabel="Idő mennyiségek"
                  [clearable]="true"
                  [formGroup]="$any(otherTime)"
                  [id]="'time' + otherTimeIndex"
                  [isRequired]="true"
                  [options]="timeUnits"
                  [searchable]="true"
                  bindLabel="title"
                  bindValue="key"
                  controlName="time"
                  label="Idő mennyisége"
                  placeholder="Válassz"
                ></mindmegette-form-select>
              </div>
            </div>
            <div class="col-12 col-md-1">
              <i (click)="otherTimeRemoveIndex = otherTimeIndex" [ngClass]="{ labelMargin: otherTimeIndex === 0 }" class="icon icon-trash" title="Törlés"></i>
            </div>
          </div>
        </ng-container>
        <div *ngIf="(formGroup?.value?.otherTimes?.length ?? 0) < maxOtherTimes" class="recipe-submit-other-time-new">
          <div (click)="addOtherTime()" class="recipe-submit-new-button">
            <span>Idő hozzáadása</span>
            <i class="icon icon-plus"></i>
          </div>
        </div>
      </div>

      <!-- Összesített idő -->
      <div class="recipe-submit-total">
        <div class="recipe-submit-total-title">Összesített idő:</div>
        <div class="recipe-submit-total-value">{{ formGroup?.value?.totalTime }} perc</div>
      </div>

      <!-- Fájlok -->
      <div class="mindmegette-form-row">
        <mindmegette-file-uploader
          [allowedExtensions]="allowedFileExtensions"
          [allowedTypes]="allowedFileTypes"
          [formGroup]="formGroup"
          [id]="'images'"
          [isRequired]="true"
          [maxFiles]="maxFiles"
          [maxSize]="maxFileSize"
          [minFiles]="minFiles"
          [showPreviewImages]="true"
          controlName="images"
          label="Kép"
        ></mindmegette-file-uploader>
      </div>
    </div>

    <!-- Értesítés -->
    <div class="recipe-submit-notification">
      <div class="recipe-submit-notification-question">Kérsz értesítést a recepted megjelenéséről?</div>
      <div class="recipe-submit-notification-wrapper">
        <label class="mindmegette-form-radio" for="shouldSendNotificationYes">
          <input [value]="true" formControlName="shouldSendNotification" id="shouldSendNotificationYes" name="shouldSendNotification" type="radio" />
          <span>Igen, kérek</span>
        </label>
        <!-- Put validation only on last element -->
        <kesma-form-control>
          <label class="mindmegette-form-radio" for="shouldSendNotificationNo">
            <input [value]="false" formControlName="shouldSendNotification" id="shouldSendNotificationNo" name="shouldSendNotification" type="radio" />
            <span>Köszönöm nem</span>
          </label>
        </kesma-form-control>
      </div>
    </div>
    @if (isNationalDish2025Enabled) {
      <div class="recipe-submit-national-dish">
        <label class="mindmegette-form-checkbox recipe-submit-national-dish-label" for="nationalDish">
          <input id="nationalDish" formControlName="isNationalDish2025" type="checkbox" />
          <span
            >Szeretnék részt venni az ország étele versenyben a beküldött receptemmel. A
            <a [routerLink]="['/', '2025-orszag-etele-jatekszabalyzat']">játékszabályzatot</a> elolvastam és elfogadom.</span
          >
        </label>
      </div>
    }
  </form>

  <div *ngIf="error" class="mindmegette-form-general-error">
    {{ error }}
  </div>

  <div class="recipe-submit-navigation">
    <mindmegette-simple-button (click)="backToPreviousStep()" [disabled]="isLoading" color="outline">Vissza </mindmegette-simple-button>
    <mindmegette-simple-button (click)="navigateToNextStep()" [disabled]="isLoading" color="primary">{{
      isLoading ? 'Kérjük várj...' : 'Beküldöm a receptet'
    }}</mindmegette-simple-button>
  </div>
</div>

<mindmegette-popup
  (resultEvent)="removeOtherTime($event)"
  *ngIf="otherTimeRemoveIndex !== null"
  [acceptButtonLabel]="'Eltávolítás'"
  [showCloseIcon]="false"
  [title]="'Megerősítés'"
>
  Biztosan eltávolítod a kiválasztott időt?
</mindmegette-popup>

<mindmegette-popup
  (resultEvent)="closeSuccessPopup()"
  *ngIf="showSuccessPopup"
  [acceptButtonLabel]="'Tovább a főoldalra'"
  [showCancelButton]="false"
  [showCloseIcon]="false"
  [title]="'Sikeres beküldés'"
>
  Köszönjük, a receptedet sikeresen rögzítettük! A jóváhagyást követően hamarosan megjelenik az oldalunkon.
</mindmegette-popup>
