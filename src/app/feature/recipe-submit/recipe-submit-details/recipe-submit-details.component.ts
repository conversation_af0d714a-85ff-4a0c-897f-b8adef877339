import { ChangeDetectionStrategy, ChangeDetectorRef, Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { RecipeSubmitCost, RecipeSubmitDetailsData, RecipeSubmitDifficulty, RecipeSubmitOtherTime, RecipeSubmitStepType } from '../recipe-submit.definitions';
import { RecipeSubmitService } from '../recipe-submit.service';
import { FormsModule, ReactiveFormsModule, UntypedFormArray, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import {
  KesmaFormControlComponent,
  KeyTitleOption,
  markControlsTouched,
  maxLengthArray,
  minLengthArray,
  nonWhitespaceOnlyValidator,
  PortalConfigSetting,
} from '@trendency/kesma-ui';
import { combineLatest, EMPTY, Subject } from 'rxjs';
import { startWith, takeUntil } from 'rxjs/operators';
import { Router, RouterLink } from '@angular/router';
import { ReCaptchaV3Service } from 'ngx-captcha';
import { environment } from '../../../../environments/environment';
import { NgClass, NgFor, NgIf } from '@angular/common';
import {
  MindmegetteButtonSelectorComponent,
  MindmegetteFileUploaderComponent,
  MindmegetteFormSelectComponent,
  MindmegettePopupComponent,
  MindmegettePortionSelectorComponent,
  MindmegetteSimpleButtonComponent,
  PortalConfigService,
  SecureApiService,
} from '../../../shared';

@Component({
  selector: 'app-recipe-submit-details',
  templateUrl: './recipe-submit-details.component.html',
  styleUrls: ['./recipe-submit-details.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NgIf,
    FormsModule,
    ReactiveFormsModule,
    KesmaFormControlComponent,
    NgFor,
    NgClass,
    MindmegettePortionSelectorComponent,
    MindmegetteButtonSelectorComponent,
    MindmegetteFormSelectComponent,
    MindmegetteFileUploaderComponent,
    MindmegetteSimpleButtonComponent,
    MindmegettePopupComponent,
    RouterLink,
  ],
})
export class RecipeSubmitDetailsComponent implements OnInit, OnDestroy {
  formGroup: UntypedFormGroup;

  difficultyOptions: KeyTitleOption<RecipeSubmitDifficulty>[] = Object.entries(RecipeSubmitDifficulty).map(([key, title]) => ({
    key: key as RecipeSubmitDifficulty,
    title: title,
  }));

  costOptions: KeyTitleOption<RecipeSubmitCost>[] = Object.entries(RecipeSubmitCost).map(([key, title]) => ({
    key: key as RecipeSubmitCost,
    title: title,
  }));

  timeUnits: KeyTitleOption<number>[] = Array.from({ length: 61 }, (_value, index: number) => ({
    key: index * 5,
    title: `${index * 5} perc`,
  }));

  otherTimeRemoveIndex: number | null = null;

  maxOtherTimes = 5;

  allowedFileTypes: string[] = ['image/png', 'image/jpeg'];
  allowedFileExtensions: string[] = ['png', 'jpg', 'jpeg'];
  maxFileSize = 5; // In MB
  minFiles = 1;
  maxFiles = 2;

  isLoading = false;
  error: string | null = null;

  showSuccessPopup = false;

  unsubscribe$: Subject<void> = new Subject<void>();

  isNationalDish2025Enabled: boolean = false;

  constructor(
    private readonly recipeSubmitService: RecipeSubmitService,
    private readonly formBuilder: UntypedFormBuilder,
    private readonly secureApiService: SecureApiService,
    private readonly router: Router,
    private readonly cdr: ChangeDetectorRef,
    private readonly reCaptchaV3Service: ReCaptchaV3Service,
    private readonly portalconfigService: PortalConfigService
  ) {}

  get recipeName(): string {
    this.isNationalDish2025Enabled = this.portalconfigService.isConfigSet(PortalConfigSetting.ENABLE_SEND_RECIPE_TO_NATIONAL_DISH_2025);
    return this.recipeSubmitService.sessionData?.recipeName ?? '';
  }

  ngOnInit(): void {
    this.initForm();
  }

  initForm(): void {
    const data: RecipeSubmitDetailsData | undefined = this.recipeSubmitService.sessionData;
    this.formGroup = this.formBuilder.group({
      portion: [data?.portion ?? 2, [Validators.required, Validators.pattern(/^[0-9]+$/), Validators.min(1), Validators.max(99)]],
      difficulty: [data?.difficulty ?? Object.keys(RecipeSubmitDifficulty)[0], [Validators.required]],
      cost: [data?.cost ?? Object.keys(RecipeSubmitCost)[0], [Validators.required]],
      comment: [data?.comment ?? null, [Validators.maxLength(1000)]],
      prepareTime: [data?.prepareTime ?? null, [Validators.required]],
      cookTime: [data?.cookTime ?? null, [Validators.required]],
      totalTime: [data?.totalTime ?? 0, [Validators.required]],
      otherTimes: this.formBuilder.array(
        (data?.otherTimes ?? []).map((otherTime: RecipeSubmitOtherTime) => this.initOtherTimeFormGroup(otherTime)),
        [maxLengthArray(this.maxOtherTimes)]
      ),
      images: this.formBuilder.array([], [minLengthArray(this.minFiles), maxLengthArray(this.maxFiles)]),
      shouldSendNotification: [data?.shouldSendNotification ?? true],
      isNationalDish2025: [false],
    });

    // Calculate total time
    combineLatest([
      this.formGroup.get('prepareTime')?.valueChanges.pipe(startWith(this.formGroup.get('prepareTime')?.value ?? 0)) ?? EMPTY,
      this.formGroup.get('cookTime')?.valueChanges.pipe(startWith(this.formGroup.get('cookTime')?.value ?? 0)) ?? EMPTY,
      this.formGroup.get('otherTimes')?.valueChanges.pipe(startWith(this.formGroup.get('otherTimes')?.value ?? [])) ?? EMPTY,
    ])
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(([prepareTime, cookTime, otherTimeObjects]) => {
        const otherTimes: number =
          otherTimeObjects
            ?.map((otherTime: RecipeSubmitOtherTime) => +(otherTime?.time ?? 0))
            .reduce((acc: number, value: number) => {
              return acc + value;
            }, 0) ?? 0;
        this.formGroup.get('totalTime')?.patchValue(prepareTime + cookTime + otherTimes);
      });
  }

  getOtherTimesFormArray(): UntypedFormArray {
    return this.formGroup.get('otherTimes') as UntypedFormArray;
  }

  initOtherTimeFormGroup(otherTime: RecipeSubmitOtherTime | null): UntypedFormGroup {
    return this.formBuilder.group({
      timeName: [otherTime?.timeName ?? null, [Validators.required, nonWhitespaceOnlyValidator, Validators.maxLength(50)]],
      time: [otherTime?.time ?? null, [Validators.required]],
    });
  }

  addOtherTime(otherTime: RecipeSubmitOtherTime | null = null): void {
    this.getOtherTimesFormArray().push(this.initOtherTimeFormGroup(otherTime));
  }

  removeOtherTime(shouldDelete: boolean): void {
    if (this.otherTimeRemoveIndex !== null) {
      if (shouldDelete) {
        this.getOtherTimesFormArray().removeAt(this.otherTimeRemoveIndex);
      }
      this.otherTimeRemoveIndex = null;
    }
  }

  backToPreviousStep(): void {
    this.recipeSubmitService.currentStep$.next(RecipeSubmitStepType.WORKFLOW);
  }

  navigateToNextStep(): void {
    if (this.formGroup) {
      markControlsTouched(this.formGroup);
    }

    if (!this.formGroup?.valid) {
      return;
    }

    this.error = null;
    this.isLoading = true;

    // First save data because there might be backend error
    this.recipeSubmitService.handleStep(RecipeSubmitStepType.DETAILS, this.formGroup?.value as RecipeSubmitDetailsData);

    this.reCaptchaV3Service.execute(
      environment.googleSiteKey ?? '',
      'app_secureapi_send_in_recipe',
      (recaptchaToken: string) => {
        this.secureApiService.submitRecipe(this.recipeSubmitService?.sessionData ?? {}, recaptchaToken).subscribe({
          next: () => {
            this.showSuccessPopup = true;
            this.isLoading = false;
            this.recipeSubmitService.resetSessionData();
            this.cdr.detectChanges();
          },
          error: () => {
            this.error = 'Ismeretlen hiba történt! Kérjük ellenőrizd az adatokat és próbáld újra vagy vedd fel a kapcsolatot a szerkesztőséggel!';
            this.isLoading = false;
            this.cdr.detectChanges();
          },
        });
      },
      {
        useGlobalDomain: false,
      },
      () => {
        this.error = 'Captcha: Robot ellenőrzés hiba!';
        this.isLoading = false;
        this.cdr.detectChanges();
      }
    );
  }

  closeSuccessPopup(): void {
    this.router.navigate(['/']);
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }
}
