import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { IMetaData, SchemaOrgService, SeoService } from '@trendency/kesma-core';
import {
  AdvertisementAdoceanComponent,
  AdvertisementAdoceanStoreService,
  AdvertisementsByMedium,
  ApiResponseMetaList,
  ApiResult,
  ArticleCard,
  ArticleSeoFields,
  createCanonicalUrlForPageablePage,
  getSchemaImageObject,
  LimitableMeta,
  PAGE_TYPES,
  RecipeCard,
} from '@trendency/kesma-ui';
import { Observable, Subject, takeUntil } from 'rxjs';
import { environment } from '../../../../../environments/environment';
import {
  AdvertisementMetaService,
  ArticleCardComponent,
  createKeywords,
  createWebPageSchema,
  defaultMetaInfo,
  ExternalRecommendationsComponent,
  getStructuredDataForRecipeList,
  MindmegetteArticleCardType,
  MindmegettePagerComponent,
  RecipeCardComponent,
  RecipeCardType,
  TitleWithPagePipe,
} from '../../../../shared';
import { SelectionItem } from '../../../static-page/components/selection-list/selection-list.service';
import { SelectionPageResolverData } from '../../selection-page.resolver';
import { SelectionData } from '../../selection-page.service';
import { selectionToArticleCard, selectionToRecipeCard } from '../../selection-utils';
import { SidebarComponent } from '../../../layout/components/sidebar/sidebar.component';
import { NgFor, NgIf, NgTemplateOutlet, SlicePipe } from '@angular/common';

@Component({
  selector: 'app-selection-page',
  templateUrl: './selection-page.component.html',
  styleUrls: ['./selection-page.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NgTemplateOutlet,
    NgIf,
    AdvertisementAdoceanComponent,
    NgFor,
    ExternalRecommendationsComponent,
    SidebarComponent,
    SlicePipe,
    RecipeCardComponent,
    ArticleCardComponent,
    MindmegettePagerComponent,
    TitleWithPagePipe,
  ],
})
export class SelectionPageComponent implements OnInit, OnDestroy {
  articles?: SelectionItem[];
  selectionData: ApiResult<SelectionData>;
  limitables?: LimitableMeta;

  adverts?: AdvertisementsByMedium;
  private readonly destroy$: Subject<boolean> = new Subject();

  readonly RecipeCardType = RecipeCardType;
  readonly ArticleCardType = MindmegetteArticleCardType;

  constructor(
    private readonly route: ActivatedRoute,
    private readonly cdr: ChangeDetectorRef,
    private readonly seo: SeoService,
    private readonly adStore: AdvertisementAdoceanStoreService,
    private readonly schemaService: SchemaOrgService,
    private readonly adoMetaService: AdvertisementMetaService
  ) {}

  ngOnInit(): void {
    (this.route.data as Observable<{ data: SelectionPageResolverData }>).subscribe((data: { data: SelectionPageResolverData }) => {
      this.articles = data?.data?.items.data;
      this.selectionData = data?.data?.selectionData;
      this.limitables = (data?.data?.items.meta as ApiResponseMetaList)?.limitable;

      this.schemaService.removeStructuredData();

      if (this.articles?.length) {
        const recipes = this.articles
          .filter((article) => !article?.column)
          .map((selection) => {
            return selectionToRecipeCard(selection);
          });

        if (recipes?.length) {
          this.schemaService.insertSchema(
            createWebPageSchema({
              url: this.seo.hostUrl,
              primaryImageOfPage: getSchemaImageObject(
                data?.data?.selectionData?.data?.coverImage?.fullSizeUrl ?? `${this.seo.hostUrl}/assets/images/placeholder.jpg`
              ),
            })
          );
          const structuredData = {
            ...getStructuredDataForRecipeList(recipes, environment?.siteUrl ?? ''),
          };
          // SEO kérésre itt ki kell szedni az image-et
          structuredData.itemListElement.forEach((item) => {
            delete item.image;
          });
          this.schemaService.insertSchema(structuredData);
        }
      }

      const selectionSlug = this.selectionData?.data?.slug;
      if (selectionSlug) {
        this.adoMetaService.set([selectionSlug]);
      }

      this.setMetaData();
      this.cdr.markForCheck();
    });
    this.initAds();
  }

  ngOnDestroy(): void {
    this.destroy$.next(true);
    this.destroy$.complete();
  }

  protected initAds(): void {
    this.resetAds();

    this.adStore.advertisemenets$.pipe(takeUntil(this.destroy$)).subscribe((ads) => {
      this.adverts = this.adStore.separateAdsByMedium(ads, PAGE_TYPES.other_pages);
      this.cdr.detectChanges();
    });
  }

  private resetAds(): void {
    this.adverts = undefined;
    this.cdr.detectChanges();
  }

  private setMetaData(): void {
    const page = (this.limitables?.pageCurrent || 0) + 1;
    const pageTitle = page > 1 ? page + '. oldal - ' : '';
    const seo = this.selectionData?.data?.seo as ArticleSeoFields;
    const title = `${seo?.seoTitle || this.selectionData?.data.title} | ${defaultMetaInfo.ogSiteName}`;
    const description = seo?.seoDescription || defaultMetaInfo.description;

    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: pageTitle.concat(title),
      ogTitle: pageTitle.concat(title),
      ogImage: this.selectionData?.data?.coverImage?.fullSizeUrl || 'https://www.mindmegette.hu/assets/images/placeholder.jpg',
      description,
      ogDescription: description,
      keywords: createKeywords(seo),
      robots: seo?.seoRobotsMeta || defaultMetaInfo.robots,
      twitterDescription: description,
    };
    this.seo.setMetaData(metaData);
    const canonical = createCanonicalUrlForPageablePage('valogatas', this.route.snapshot);
    if (canonical) this.seo.updateCanonicalUrl(canonical);
  }

  public selectionToRecipeCard(item: SelectionItem): RecipeCard {
    return selectionToRecipeCard(item);
  }

  public selectionToArticleCard(item: SelectionItem): ArticleCard {
    return selectionToArticleCard(item);
  }
}
