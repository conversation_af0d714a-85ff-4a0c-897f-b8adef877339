import { inject, Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { ReqService } from '@trendency/kesma-core';
import { WeekDays, WeeklyMenu } from './weekly-menu.definitions';
import { ApiResult, RecipeCard } from '@trendency/kesma-ui';
import { ActivatedRoute } from '@angular/router';
import { map, shareReplay, switchMap } from 'rxjs/operators';
import { backendRecipeSearchResponseToRecipeCard } from '../search-page/search-page.utils';
import { LabelSelectWithRecipesType } from '../../shared';

const WEEK_DAYS: WeekDays[] = [
  { title: 'Hétfő', index: 1 },
  { title: 'Kedd', index: 2 },
  { title: 'Szerda', index: 3 },
  { title: 'Csütörtök', index: 4 },
  { title: 'Péntek', index: 5 },
  { title: 'Szombat', index: 6 },
  { title: 'Vasárnap', index: 0 },
];

@Injectable({
  providedIn: 'root',
})
export class WeeklyMenuService {
  private readonly route = inject(ActivatedRoute);
  private readonly reqService = inject(ReqService);

  readonly weeklyMenu: Observable<WeeklyMenu> = this.getWeeklyMenu().pipe(shareReplay({ bufferSize: 1, refCount: true }));

  private readonly _weeklyMenuDataForLayout$: Observable<LabelSelectWithRecipesType[]> = this.weeklyMenu.pipe(
    map(({ weeklyMenuItems }) => {
      const menuItems: LabelSelectWithRecipesType[] = [];
      if (!weeklyMenuItems?.length) {
        return [];
      }

      WEEK_DAYS.forEach((weekDay: WeekDays) => {
        const day = weeklyMenuItems.find((menuItem) => weekDay.index === new Date(menuItem?.day)?.getDay());
        if (!day) {
          menuItems.push({
            title: weekDay.title,
            recipes: [],
          });
        } else {
          menuItems.push({
            title: weekDay.title,
            recipes: [
              day.eloetel ? backendRecipeSearchResponseToRecipeCard(day?.eloetel) : undefined,
              day.foetel ? backendRecipeSearchResponseToRecipeCard(day?.foetel) : undefined,
              day.desszert ? backendRecipeSearchResponseToRecipeCard(day?.desszert) : undefined,
              day.vega ? backendRecipeSearchResponseToRecipeCard(day?.vega) : undefined,
            ].filter((recipe) => !!recipe) as RecipeCard[],
          });
        }
      });
      return menuItems;
    }),
    shareReplay({ bufferSize: 1, refCount: true })
  );

  readonly weeklyMenuDataForLayout$ = this.route.data.pipe(
    switchMap(({ weeklyMenu }) => {
      return weeklyMenu ? of(weeklyMenu) : this._weeklyMenuDataForLayout$;
    })
  );

  getWeeklyMenu(): Observable<WeeklyMenu> {
    return this.reqService.get<WeeklyMenu>(`weekly-menu/this-week`);
  }

  getWeeklyMenuPreview(previewHash: string): Observable<ApiResult<WeeklyMenu>> {
    return this.reqService.get<ApiResult<WeeklyMenu>>(`weekly-menu/preview/view?previewHash=${previewHash}`);
  }
}
