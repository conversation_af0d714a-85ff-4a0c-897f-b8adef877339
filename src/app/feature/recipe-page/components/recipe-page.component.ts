import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Ng<PERSON><PERSON>, Ng<PERSON><PERSON>C<PERSON>, NgT<PERSON>plateOutlet, SlicePipe } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, inject, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute, RouterLink } from '@angular/router';
import { isDesktopAdShared } from '@shared/utils/advert.utils';
import { IMetaData, PublishDatePipe, SchemaOrgService, SeoService, UtilService } from '@trendency/kesma-core';
import {
  Advertisement,
  AdvertisementAdoceanComponent,
  AdvertisementAdoceanStoreService,
  AdvertisementBannerName,
  ALL_BANNER_LIST,
  ArticleAdvertisements,
  ArticleBodyType,
  ArticleCard,
  ArticleFileLinkDirective,
  ArticleSeoFields,
  ArticleSocial,
  ArticleVideoComponent,
  createCanonicalUrlForPageablePage,
  getSchemaImageObject,
  PAGE_TYPES,
  <PERSON>cipeCard,
  RoutingHelperService,
  Tag,
} from '@trendency/kesma-ui';
import { BehaviorSubject, Observable, of, Subject, timer } from 'rxjs';
import { map, startWith, switchMap, takeUntil } from 'rxjs/operators';
import {
  AdvertisementMetaService,
  ArticleInfoBoxComponent,
  ArticleSocialShareComponent,
  AuthService,
  CategoryLabelComponent,
  CategoryLabelTypes,
  createKeywords,
  createWebPageSchema,
  defaultMetaInfo,
  ExternalRecommendationsComponent,
  extractVideoSchemasFromWysiwyg,
  getStructuredDataForRecipe,
  IArticleInfoBox,
  MindmegetteArticleCardType,
  MindmegetteWysiwygBoxComponent,
  ProfileBadgesComponent,
  RecipeEnergyContentComponent,
  SecureApiService,
  TagComponent,
  WrapTablePipe,
  WtfDateToDatePipe,
} from '../../../shared';
import { RecommendedArticlesComponent } from '../../article-page/components/recommended-articles/recommended-articles.component';
import { CommentSectionComponent } from '../../comment-section/components/comment-section/comment-section/comment-section.component';
import { SidebarComponent } from '../../layout/components/sidebar/sidebar.component';
import { Recipe, RecipeGroup } from '../recipe-page.definitions';
import { RecipePageService } from '../recipe-page.service';
import { RecipeIngredientsContentComponent } from './recipe-ingredients-content/recipe-ingredients-content.component';
import { RecipeRecommendationContentComponent } from './recipe-most-popular-content/recipe-recommendation-content.component';
import { RecipeRatingComponent } from './recipe-rating/recipe-rating.component';
import { RecipeSaverPopoverComponent } from './recipe-saver-popover/recipe-saver-popover.component';
import { RecipeSecondaryThumbnailComponent } from './recipe-secondary-thumbnail/recipe-secondary-thumbnail.component';
import { BreadcrumbComponent } from '@shared/components/breadcrumb/breadcrumb.component';

@Component({
  selector: 'app-recipe-page',
  templateUrl: 'recipe-page.component.html',
  styleUrls: ['recipe-page.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NgIf,
    NgTemplateOutlet,
    NgFor,
    NgClass,
    RouterLink,
    ProfileBadgesComponent,
    RecipeSaverPopoverComponent,
    ArticleSocialShareComponent,
    RecipeIngredientsContentComponent,
    AdvertisementAdoceanComponent,
    RecipeEnergyContentComponent,
    ArticleInfoBoxComponent,
    RecipeRatingComponent,
    RecipeRecommendationContentComponent,
    CommentSectionComponent,
    RecommendedArticlesComponent,
    ExternalRecommendationsComponent,
    SidebarComponent,
    RecipeSecondaryThumbnailComponent,
    NgSwitch,
    NgSwitchCase,
    ArticleFileLinkDirective,
    ArticleVideoComponent,
    AsyncPipe,
    SlicePipe,
    PublishDatePipe,
    WrapTablePipe,
    WtfDateToDatePipe,
    CategoryLabelComponent,
    TagComponent,
    MindmegetteWysiwygBoxComponent,
    BreadcrumbComponent,
  ],
  providers: [WtfDateToDatePipe],
})
export class RecipePageComponent implements OnInit, OnDestroy {
  recipe?: Recipe & { breadcrumbTitle?: string };
  shareUrl?: string;
  thumbnailUrls?: string[];
  similarRecipes?: ArticleCard[];
  recipeRecommendations?: RecipeCard[];
  freshRecipes?: RecipeCard[];
  tagsAndCategories?: Tag[];
  freshArticles: ArticleCard[];
  adverts?: ArticleAdvertisements;
  infoBox?: IArticleInfoBox;
  adPageType = PAGE_TYPES.recipe_pages;
  box_1_tablet?: Advertisement;
  box_2_tablet?: Advertisement;
  box_3_tablet?: Advertisement;
  showDesktopAdLocal: boolean;
  mobilrectangle_ottboxextra_tablet?: Advertisement;
  socialCount: ArticleSocial = {};
  preferredSelection?: { id: string; slug: string };

  localAdverts: Advertisement[][];

  savedRecipeGroups$: Observable<RecipeGroup[]>;
  readonly isVoted$ = new BehaviorSubject(false);

  readonly CategoryLabelTypes = CategoryLabelTypes;
  readonly ArticleCardType = MindmegetteArticleCardType;
  readonly ArticleBodyType = ArticleBodyType;
  readonly MAX_THUMBNAIL_SIZE = 2;

  private readonly destroy$: Subject<boolean> = new Subject();
  savedIngredientsNotification$: Observable<boolean | null>;
  private readonly savedIngredientsNotificationSubject$: Observable<void> = this.recipeService.recipeIngredientNotification$;

  readonly #wtfDateToDatePipe = inject(WtfDateToDatePipe);

  constructor(
    private readonly route: ActivatedRoute,
    private readonly cdr: ChangeDetectorRef,
    private readonly routingHelperService: RoutingHelperService,
    private readonly secureApiService: SecureApiService,
    private readonly adStoreAdo: AdvertisementAdoceanStoreService,
    private readonly authService: AuthService,
    private readonly seo: SeoService,
    private readonly adoMetaService: AdvertisementMetaService,
    private readonly schemaService: SchemaOrgService,
    private readonly utils: UtilService,
    private readonly recipeService: RecipePageService,
    private readonly utilsService: UtilService
  ) {}

  get publishDates(): {
    original: string;
    updated?: string;
  } {
    const firstPublishDate = this.recipe?.firstPublishDate ?? '';
    const latestPublishDate = this.recipe?.publishDate ?? '';

    if (!firstPublishDate || !latestPublishDate || firstPublishDate === latestPublishDate) {
      return {
        original: latestPublishDate ?? firstPublishDate ?? new Date(),
      };
    }

    return {
      original: firstPublishDate,
      updated: latestPublishDate,
    };
  }

  ngOnInit(): void {
    this.route.data
      .pipe(
        switchMap(({ data }) => {
          this.adStoreAdo.setArticleParentCategory(this.adPageType);
          const { recipe, recipeRecommendations, isVoted, freshRecipes, socialCount, similarRecipes, freshArticles, meta } = data;

          this.socialCount = socialCount;
          this.preferredSelection = meta?.preferredSelection;
          this.freshRecipes = freshRecipes;
          this.similarRecipes = similarRecipes;
          this.isVoted$.next(!!isVoted?.[0]?.hasRecipeRate);
          this.freshArticles = freshArticles;
          this.recipe = {
            ...recipe,
            publicAuthor: recipe?.publicAuthor
              ? {
                  ...recipe?.publicAuthor,
                  avatarFullSizeUrl: recipe?.publicAuthor?.avatar?.thumbnailUrl,
                }
              : null,
          };
          this.infoBox = {
            type: 'comment',
            title: 'Megjegyzés',
            description: this.recipe?.description || '',
          };
          this.recipeRecommendations = recipeRecommendations;
          this.shareUrl = this.routingHelperService.resolveLink(['/', 'recept', this.recipe?.slug as string]);
          this.getTagsAndCategories(recipe);
          this.setMetaData();
          this.setThumbnailUrls();

          // RESET ADOCEAN META TAGS
          const advertMeta = this.adStoreAdo.advertMeta$.getValue();
          this.adStoreAdo.advertMeta$.next({
            vars: advertMeta.vars,
            keys: '',
          });

          const tagSlugs = this.recipe?.tags?.map((tag) => tag?.slug) as string[];
          const categorySlugs = this.recipe?.categories?.map((category) => category?.slug) as string[];
          const ingredientSlugs = this.recipe?.recipeIngredients?.map((ingredient) => ingredient?.ingredient?.slug) as string[];
          const parentCategorySlugs = this.recipe?.parentCategories?.map((category) => category?.slug) as string[];

          //key=teszt,gluténmentes=amerikai,angol=banán,borsó/mwid=...
          this.adoMetaService.set(tagSlugs);
          this.adoMetaService.set(categorySlugs);
          this.adoMetaService.set(ingredientSlugs);
          this.adoMetaService.set(parentCategorySlugs);
          if (this.preferredSelection) {
            this.adoMetaService.set([this.preferredSelection.slug]);
          }

          if (this.recipe) {
            this.schemaService.removeStructuredData();
            const recipeSchema: Recipe = {
              ...recipe,
              coverImage: {
                ...recipe.coverImage,
                thumbnailUrl: recipe?.coverImage?.thumbnailUrl || `${this.seo.hostUrl}/assets/images/placeholder.jpg`,
              },
            };
            this.schemaService.insertSchema(
              createWebPageSchema({
                url: this.seo.hostUrl,
                primaryImageOfPage: getSchemaImageObject(
                  recipe?.coverImage?.fullSizeUrl ?? recipe?.coverImage?.thumbnailUrl ?? `${this.seo.hostUrl}/assets/images/placeholder.jpg`
                ),
              })
            );
            this.schemaService.insertSchema(getStructuredDataForRecipe(recipeSchema));
            extractVideoSchemasFromWysiwyg(this.recipe.makingText, {
              name: `${this.recipe.title} | Mindmegette.hu`,
              description: this.recipe.description,
              uploadDate: this.#wtfDateToDatePipe.transform(this.recipe.publishDate ?? ''),
              dateCreated: this.#wtfDateToDatePipe.transform(this.recipe.publishDate ?? ''),
              thumbnailUrl: this.recipe.coverImage?.thumbnailUrl ?? `${this.seo.hostUrl}/assets/images/placeholder.jpg`,
            }).forEach((schema) => this.schemaService.insertSchema(schema));
          }

          this.initAds();
          this.cdr.markForCheck();

          return this.authService.isAuthenticated();
        }),
        takeUntil(this.destroy$)
      )
      .subscribe((isAuthenticated: boolean) => {
        this.savedRecipeGroups$ = isAuthenticated
          ? this.secureApiService.getSavedRecipeGroups(this.recipe?.slug as string).pipe(map(({ data }) => data))
          : of([]);
      });

    this.savedIngredientsNotification$ = this.utils.isBrowser()
      ? this.savedIngredientsNotificationSubject$.pipe(
          switchMap(() => {
            return timer(3000).pipe(
              map(() => false),
              startWith(true)
            );
          })
        )
      : of(null);
  }

  ngOnDestroy(): void {
    this.destroy$.next(true);
    this.destroy$.complete();
    this.isVoted$.complete();
    this.adStoreAdo.setArticleParentCategory('');
  }

  resetAds(): void {
    this.adverts = undefined;
    this.cdr.detectChanges();
  }

  get hasAllergics(): boolean {
    return this.recipe?.allergics?.some(({ icon }) => !!icon?.thumbnailUrl) || false;
  }

  getRoundedRateAverage(rateAverage: string): number {
    return parseFloat(parseFloat(rateAverage).toFixed(1));
  }

  private initAds(): void {
    this.resetAds();

    this.showDesktopAdLocal = isDesktopAdShared(this.utilsService.isBrowser(), 1400);

    this.adStoreAdo.advertisemenets$.pipe(takeUntil(this.destroy$)).subscribe((ads) => {
      const localBanners = [
        'szovegszponz_1',
        'szovegszponz_2',
        'hozzavalok_1',
        'hozzavalok_2',
        'hozzavalok_3',
        'hozzavalok_4',
        'hozzavalok_5',
      ] as unknown as AdvertisementBannerName[];

      this.adverts = this.adStoreAdo.separateAdsByMedium(ads, this.adPageType, [
        ...ALL_BANNER_LIST,
        ...localBanners,
        'box_hozzavalok',
        'mobilrectangle_hozzavalok',
      ] as AdvertisementBannerName[]);

      const mobileLocalAds = localBanners.map((banner) => this.adverts?.mobile?.[banner]);
      const desktopLocalAds = localBanners.map((banner) => this.adverts?.desktop?.[banner]);

      this.localAdverts = [mobileLocalAds, desktopLocalAds] as Advertisement[][];

      this.box_1_tablet = { ...this.adverts?.mobile?.['mobilrectangle_1'], medium: 'desktop' };
      this.box_2_tablet = { ...this.adverts?.mobile?.['mobilrectangle_2'], medium: 'desktop' };
      this.box_3_tablet = {
        ...this.adverts?.mobile?.['mobilrectangle_3'],
        medium: 'desktop',
      };
      this.mobilrectangle_ottboxextra_tablet = {
        ...this.adverts?.mobile?.['mobilrectangle_ottboxextra'],
        medium: 'desktop',
      };

      this.cdr.detectChanges();
    });
  }

  private setMetaData(): void {
    const title = `${this.recipe?.seo?.seoTitle || this.recipe?.title + ' Recept képpel'} | ${defaultMetaInfo.ogSiteName}`;

    //eslint-disable-next-line max-len
    const defaultDescription = `Készítsd el a ${this.recipe?.title} kipróbált, bevált receptjét. A Mindmegette.hu receptgyűjteményében mindent megtalálsz.`;
    const description = this.recipe?.seo?.seoDescription || defaultDescription;

    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title,
      ogTitle: title,
      ogImage: this.recipe?.coverImage?.fullSizeUrl || this.recipe?.secondaryCoverImage?.fullSizeUrl,
      description,
      ogDescription: description,
      keywords:
        createKeywords(this.recipe?.seo as ArticleSeoFields) ||
        `${this.recipe?.title}` +
          (this.recipe?.tags?.length ? `, ${this.recipe?.tags?.map(({ title }) => title?.trim()).join(', ')}` : '') +
          (this.recipe?.categories?.length ? `, ${this.recipe?.categories?.map(({ title }) => title?.trim()).join(', ')}` : '') +
          (this.recipe?.difficulty?.title ? `, ${this.recipe?.difficulty?.title}` : '') +
          (this.recipe?.cost?.title ? `, ${this.recipe?.cost?.title}` : '') +
          (this.recipe?.publicAuthor?.fullName ? ` - ${this.recipe?.publicAuthor?.fullName}` : ''),
      robots: this.recipe?.seo?.seoRobotsMeta || defaultMetaInfo.robots,
      author: this.recipe?.publicAuthor?.fullName ?? '',
      articlePublishedTime: this.recipe?.publishDate ? this.#wtfDateToDatePipe.transform(this.recipe?.publishDate)?.toISOString() : '',
      articleModifiedTime: this.recipe?.lastUpdatedAt ?? '',
    };
    this.seo.setMetaData(metaData, { skipSeoMetaCheck: true });
    const canonical = createCanonicalUrlForPageablePage(`recept/${this.recipe?.slug}`, this.route.snapshot);
    canonical && this.seo.updateCanonicalUrl(canonical, { skipSeoMetaCheck: true });
  }

  private setThumbnailUrls(): void {
    this.thumbnailUrls = [];
    if (this.recipe?.coverImage?.thumbnailUrl) {
      this.thumbnailUrls.push(this.recipe?.coverImage?.thumbnailUrl);
    }
    if (this.recipe?.secondaryCoverImage?.thumbnailUrl) {
      this.thumbnailUrls.push(this.recipe?.secondaryCoverImage?.thumbnailUrl);
    }
  }

  getTagsAndCategories(recipe: Recipe): void {
    const tagsWithPath = recipe?.tags?.map((tag: Tag) => ({
      ...tag,
      path: 'cimke',
    }));
    const categoriesWithPath = recipe?.categories?.map((category: Tag) => ({
      ...category,
      path: 'receptkategoria',
    }));
    this.tagsAndCategories = tagsWithPath?.concat(categoriesWithPath);
  }
}
