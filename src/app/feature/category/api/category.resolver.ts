import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { map, Observable, of, switchMap, throwError } from 'rxjs';
import { CategoryResolverResponse } from './category.definitions';
import { CategoryService } from './category.service';
import { Layout, LayoutApiData } from '@trendency/kesma-ui';
import { GastroLayoutFillService } from '../../../shared';
import { catchError } from 'rxjs/operators';

@Injectable()
export class CategoryResolver {
  constructor(
    private readonly categoryService: CategoryService,
    private readonly gastroLayoutFillService: GastroLayoutFillService,
    private readonly router: Router
  ) {}

  public resolve(route: ActivatedRouteSnapshot): Observable<CategoryResolverResponse> {
    const params = route.params;
    const queryParams = route.queryParams;

    return this.categoryService.getRequestForCategoryLayout(params, queryParams).pipe(
      switchMap((res) => {
        if (!res?.layoutApiResponse) {
          return of(res);
        }

        return this.gastroLayoutFillService.fillLayoutData(res.layoutApiResponse as LayoutApiData).pipe(
          map((a) => {
            return { ...res, layoutApiResponse: { ...res.layoutApiResponse, ...a } as Layout };
          }),
          catchError((error) => {
            this.router
              .navigate(['/', '404'], {
                state: { errorResponse: JSON.stringify(error) },
                skipLocationChange: false,
              })
              .then();
            return throwError(() => error);
          })
        );
      })
    );
  }
}
