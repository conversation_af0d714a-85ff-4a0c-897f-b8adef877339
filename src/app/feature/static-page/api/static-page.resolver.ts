import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { Observable, of, throwError } from 'rxjs';
import { catchError, map, switchMap } from 'rxjs/operators';
import { Injectable } from '@angular/core';
import { GastroLayoutFillService } from '../../../shared';
import { StaticPageService } from './static-page.service';
import { HttpErrorResponse } from '@angular/common/http';

@Injectable({
  providedIn: 'root',
})
export class StaticPageResolver {
  constructor(
    private readonly staticPageService: StaticPageService,
    private readonly gastroLayoutFillService: GastroLayoutFillService,
    private readonly router: Router
  ) {}

  public resolve(route: ActivatedRouteSnapshot): Observable<any> {
    const { gastroSlug, slug } = route.params;
    const previewHash = route.queryParams['previewHash'];
    const staticSlug = gastroSlug && slug ? `${gastroSlug}-${slug}` : slug || gastroSlug;

    const request$ = previewHash ? this.staticPageService.getStaticPagePreview(staticSlug, previewHash) : this.staticPageService.getStaticPage(staticSlug);

    return request$.pipe(
      switchMap((res) => {
        if ((res.meta as any).customStaticPageType === 'staticPage') {
          // Static page
          return of(res);
        }
        // Custom built page
        return this.gastroLayoutFillService.fillLayoutData(res.data as any).pipe(
          map((a) => {
            return { ...res, data: a };
          })
        );
      }),
      catchError((error: HttpErrorResponse | Error) => {
        this.router.navigate(['/', '404'], {
          state: { errorResponse: JSON.stringify(error) },
          skipLocationChange: true,
        });
        return throwError(() => error);
      })
    );
  }
}
