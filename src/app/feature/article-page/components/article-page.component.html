<ng-container *ngIf="!isUserAdultChoice && article?.isAdultsOnly; else adultContent">
  <mindmegette-adult (isUserAdult)="onIsUserAdultChoose($event)"></mindmegette-adult>
</ng-container>

<ng-template #adultContent>
  <main>
    <section>
      <div class="wrapper">
        @if (article) {
          <app-breadcrumb
            [data]="[{ label: article.columnTitle ?? '', url: '/rovat/' + article.columnSlug }, { label: article.breadcrumbTitle || article.title }]"
          />
          <app-article-header [data]="article" class="wrapper-row"></app-article-header>
        }
      </div>
      <kesma-advertisement-adocean
        *ngIf="!showDesktopAdLocal && adverts?.mobile?.['mobilrectangle_1'] as ad"
        [ad]="ad"
        [isWidthCheckingDisabled]="true"
        [isExceptionAdvertEnabled]="isExceptionAdvertEnabled"
        [style]="{ margin: 'var(--ad-margin)' }"
      >
      </kesma-advertisement-adocean>

      <div class="wrapper with-aside two-sided">
        <div class="left-column">
          <app-article-social-share [emailSubject]="article?.title!"></app-article-social-share>
          <mindmegette-tag *ngIf="article?.tags?.length" [data]="article?.tags"></mindmegette-tag>
        </div>

        <div class="center">
          <a (click)="refreshMinuteToMinute()" *ngIf="article?.minuteToMinute === MinuteToMinuteState.RUNNING" class="reload">
            <i class="icon mindmegette-icon-reload"></i>
            Frissítés
          </a>

          <div *ngIf="article?.minuteToMinute === MinuteToMinuteState.RUNNING" class="reload-info">
            <h5 class="reload-info-info">Cikkünk folyamatosan frissül</h5>
            <span class="reload-info-number">{{ article?.minuteToMinuteBlocks?.length }} bejegyzés</span>
          </div>

          <div *ngIf="article?.minuteToMinute !== MinuteToMinuteState.RUNNING" class="article-lead">{{ article?.excerpt }}</div>

          <div *ngIf="embedPrAdvert" [innerHTML]="embedPrAdvert" class="article-embed-pr-advert"></div>

          <kesma-advertisement-adocean
            *ngIf="showDesktopAdLocal && adverts?.desktop?.['roadblock_1'] as ad"
            [ad]="ad"
            [isExceptionAdvertEnabled]="isExceptionAdvertEnabled"
            [style]="{ margin: 'var(--ad-margin)' }"
          >
          </kesma-advertisement-adocean>

          <ng-container [ngTemplateOutletContext]="{ body: article?.body }" [ngTemplateOutlet]="bodyContent"></ng-container>

          <div #dataTrigger *ngIf="article"></div>

          @if (sponsoredTag) {
            <app-sponsored-tag-box [sponsoredTag]="sponsoredTag" [excludedSlug]="article?.slug || ''" />
          }

          <ng-container [ngTemplateOutletContext]="{ data: article?.minuteToMinuteBlocks }" [ngTemplateOutlet]="minuteToMinutesTemplate"></ng-container>
          <kesma-nativterelo [tereloUrl]="tereloUrl"></kesma-nativterelo>
          <app-recommended-articles *ngIf="articleRecommendations?.length > 0" [data]="articleRecommendations | slice: 0 : 9"></app-recommended-articles>

          <app-external-recommendations
            [mobilrectangle_ottboxextra]="adverts?.mobile?.['mobilrectangle_ottboxextra']"
            [roadblock_ottboxextra]="adverts?.desktop?.['roadblock_ottboxextra']"
            [mobilrectangle_ottboxextra_tablet]="mobilrectangle_ottboxextra_tablet"
            [showDesktopAdLocal]="showDesktopAdLocal"
          ></app-external-recommendations>

          <div class="wrapper">
            <kesma-advertisement-adocean
              *ngIf="!showDesktopAdLocal && adverts?.mobile?.['mobilrectangle_2'] as ad"
              [ad]="ad"
              [isWidthCheckingDisabled]="true"
              [style]="{ margin: 'var(--ad-margin)' }"
            >
            </kesma-advertisement-adocean>

            <kesma-advertisement-adocean
              *ngIf="showDesktopAdLocal && adverts?.desktop?.['roadblock_2'] as ad"
              [ad]="ad"
              [style]="{ margin: 'var(--ad-margin)' }"
            >
            </kesma-advertisement-adocean>
          </div>

          <app-comment-section
            #commentSection
            (commentSubmitted)="handleCommentSubmitted()"
            *ngIf="article && !socialCount.isCommentsDisabled"
            [articleID]="article.id"
            [commentCount]="socialCount.commentCount ?? 0"
            id="kommentek"
            type="article"
          >
          </app-comment-section>

          <div class="wrapper">
            <kesma-advertisement-adocean
              *ngIf="!showDesktopAdLocal && adverts?.mobile?.['mobilrectangle_3'] as ad"
              [ad]="ad"
              [isWidthCheckingDisabled]="true"
              [style]="{ margin: 'var(--ad-margin)' }"
            >
            </kesma-advertisement-adocean>

            <kesma-advertisement-adocean
              *ngIf="showDesktopAdLocal && adverts?.desktop?.['roadblock_3'] as ad"
              [ad]="ad"
              [style]="{ margin: 'var(--ad-margin)' }"
            >
            </kesma-advertisement-adocean>
          </div>

          <app-recommended-articles *ngIf="freshArticles?.length" [data]="freshArticles | slice: 0 : 6" title="Friss sztorik"></app-recommended-articles>

          <kesma-google-news [data]="googleNewsData"></kesma-google-news>
        </div>

        <aside>
          <app-sidebar [adPageType]="adPageType" [articleId]="article?.id" [articleSlug]="articleSlug"></app-sidebar>
        </aside>
      </div>
    </section>
  </main>
</ng-template>

<ng-template #bodyContent let-body="body">
  <ng-container *ngFor="let element of body">
    <ng-container [ngSwitch]="element.type">
      <ng-container *ngSwitchCase="ArticleBodyType.Wysywyg">
        <ng-container *ngFor="let wysiwygDetail of element?.details">
          <mindmegette-wysiwyg-box [html]="(wysiwygDetail?.value | wrapTable) || '' | toLinkify: articleMeta!" trArticleFileLink></mindmegette-wysiwyg-box>
        </ng-container>
      </ng-container>

      <ng-container *ngSwitchCase="ArticleBodyType.Article">
        <!-- Author doesn't appear. It seems BE doesn't send publicAuthor property in dbcache -->
        <mindmegette-article-card
          [data]="simpleArticleRecommendation(element?.details[0])"
          [styleID]="ArticleCardType.LeftImageLongCard"
        ></mindmegette-article-card>
      </ng-container>

      <ng-container *ngSwitchCase="ArticleBodyType.Quiz">
        <mindmegette-quiz [data]="element?.details[0]?.value"></mindmegette-quiz>
      </ng-container>

      <ng-container *ngSwitchCase="ArticleBodyType.RossmannQuiz">
        <mindmegette-quiz [styleID]="MindmegetteQuizEnum.Rossmann" [data]="element?.details[0]?.value"></mindmegette-quiz>
      </ng-container>

      <ng-container *ngSwitchCase="ArticleBodyType.Voting">
        <mindmegette-voting
          *ngIf="getVoteData(element?.details[0]?.value) as voteData"
          [extractorData]="voteData"
          [data]="voteData.data"
          [showResults]="voteData.showResults"
          [sponsored]="voteData.sponsorship"
          [voteId]="voteData.votedId"
        >
        </mindmegette-voting>
      </ng-container>

      <ng-container *ngSwitchCase="ArticleBodyType.Gallery">
        <mindmegette-slider-gallery
          (fullscreenLayerClicked)="openGalleryDedicatedRouteLayer($event)"
          (slideChanged)="handleGallerySlideChange(galleriesData[element?.details[0]?.value?.id], $event)"
          *ngIf="galleriesData[element?.details[0]?.value?.id]"
          [data]="galleriesData[element?.details[0]?.value?.id]"
          [isInsideAdultArticleBody]="article?.isAdultsOnly"
        ></mindmegette-slider-gallery>
      </ng-container>

      <ng-container *ngSwitchCase="ArticleBodyType.Recipe">
        <div class="recipe-card">
          <div class="recipe-card-left">
            <div class="recipe-card-left-wrapper">
              <div class="category-label-wrapper">
                <mindmegette-category-label [styleID]="CategoryLabelType.RECIPE" [title]="'Recept'"></mindmegette-category-label>
                <mindmegette-category-label
                  *ngIf="element?.details?.[0]?.value?.difficulty as difficulty"
                  [styleID]="CategoryLabelType.CATEGORY"
                  [title]="difficulty"
                ></mindmegette-category-label>
              </div>
              <a [routerLink]="['/', 'recept', element?.details[0]?.value?.slug]" class="recipe-title">{{ element?.details[0]?.value?.title }}</a>
              <div class="recipe-card-details">
                <div class="recipe-card-meta divider">{{ element?.details[0]?.value?.totalTime }} perc</div>
                <div [ngClass]="{ divider: element?.details[0]?.value?.cost }" class="recipe-card-meta">
                  {{ element?.details[0]?.value?.madeForPeople }} adag
                </div>
                <div *ngIf="element?.details[0]?.value?.cost" class="recipe-card-meta">{{ element?.details[0]?.value?.cost }}</div>
              </div>
              <a [routerLink]="['/', 'recept', element?.details[0]?.value?.slug]" class="recipe-button">
                Tovább a recepthez
                <i class="icon mindmegette-icon-white-right-arrow"></i>
              </a>
            </div>
          </div>
          <div class="recipe-card-right">
            <img
              withFocusPoint
              [data]="element?.details[0]?.value?.coverImageFocusedImages"
              [alt]="'recept'"
              [displayedUrl]="element?.details[0]?.value?.coverImage || '/assets/images/placeholder.jpg'"
              [displayedAspectRatio]="{ desktop: '1:1' }"
              class="thumbnail"
              loading="lazy"
            />
          </div>
        </div>
      </ng-container>

      <ng-container *ngSwitchCase="ArticleBodyType.NewsletterSignUp">
        <mindmegette-newsletter [styleID]="MindmegetteNewsletterCardType.LeftImageCard"></mindmegette-newsletter>
      </ng-container>

      <ng-container *ngSwitchCase="ArticleBodyType.MediaVideo">
        <kesma-article-video [data]="element?.details[0]?.value"></kesma-article-video>
      </ng-container>

      <ng-container *ngSwitchCase="ArticleBodyType.Infobox">
        <app-article-info-box [data]="getInfoBoxData(element?.details)"></app-article-info-box>
      </ng-container>

      <ng-container *ngSwitchCase="ArticleBodyType.Advert">
        <kesma-advertisement-adocean
          *ngIf="!showDesktopAdLocal && adverts?.mobile?.[element.adverts.mobile] as ad"
          [ad]="ad"
          [isWidthCheckingDisabled]="true"
        ></kesma-advertisement-adocean>
        <kesma-advertisement-adocean *ngIf="showDesktopAdLocal && adverts?.desktop?.[element.adverts.desktop] as ad" [ad]="ad"></kesma-advertisement-adocean>
      </ng-container>

      <ng-container *ngSwitchCase="ArticleBodyType.SecretDaysCalendar">
        <app-secret-days-calendar-adapter [id]="element?.details[0]?.value?.id"></app-secret-days-calendar-adapter>
      </ng-container>
    </ng-container>
  </ng-container>
</ng-template>

<ng-template #minuteToMinutesTemplate let-data="data">
  <ng-container *ngFor="let element of data; index as index">
    <div [id]="'pp-' + (index + 1)" class="pp-scroll-target"></div>
    <div class="card">
      <div class="date-wrapper">
        <div class="date">
          <span class="hhmm">{{ element?.date | formatDate: 'h-m' }}</span>
          <span class="full-date">{{ getMinuteToMinuteDate(element?.date) | titlecase }}</span>
        </div>
        <app-article-social-share [emailSubject]="article?.title!" [minuteToMinuteId]="index + 1" [showOnlyUrlCopy]="true"></app-article-social-share>
      </div>
      <h2 class="card-title">{{ element?.title }}</h2>
      <mindmegette-wysiwyg-box [html]="(element?.body[0]?.details[0]?.value | wrapTable) || ''" trArticleFileLink></mindmegette-wysiwyg-box>
    </div>
  </ng-container>
</ng-template>
