import { DOCUMEN<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>For, NgIf, NgS<PERSON>, NgSwitchCase, NgTemplateOutlet, SlicePipe, TitleCasePipe } from '@angular/common';
import { AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, ElementRef, Inject, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { FormatDatePipe, IMetaData, SchemaOrg, SchemaOrgService, SeoService, StorageService, UtilService } from '@trendency/kesma-core';
import {
  Advertisement,
  AdvertisementAdoceanComponent,
  AdvertisementAdoceanStoreService,
  ALL_BANNER_LIST,
  AnalyticsService,
  ApiResponseMeta,
  Article,
  ArticleAdvertisements,
  ArticleBody,
  ArticleBodyDetails,
  ArticleBodyType,
  ArticleCard,
  ArticleCardWithSocial,
  ArticleFileLinkDirective,
  ArticleResolverData,
  ArticleRouteParams,
  ArticleSocial,
  ArticleVideoComponent,
  AutoArticleBodyAdService,
  buildArticleUrl,
  FocusPointDirective,
  GalleryData,
  GalleryElementData,
  getSchemaImageObject,
  getStructuredDataForArticle,
  GoogleNewsComponent,
  GoogleNewsData,
  MinuteToMinuteState,
  NativtereloComponent,
  PAGE_TYPES,
  previewBackendArticleToArticleCard,
  SecondaryFilterAdvertType,
  SponsoredTag,
  VoteData,
  VoteDataWithAnswer,
  VoteService,
} from '@trendency/kesma-ui';
import { combineLatest, forkJoin, Observable, Subject } from 'rxjs';
import { map, switchMap, takeUntil } from 'rxjs/operators';
import { environment } from '../../../../environments/environment';
import {
  AdvertisementMetaService,
  ArticleCardComponent,
  ArticleInfoBoxComponent,
  ArticleSocialShareComponent,
  CategoryLabelComponent,
  CategoryLabelTypes,
  createWebPageSchema,
  defaultMetaInfo,
  ExternalRecommendationsComponent,
  extractVideoSchemasFromWysiwyg,
  GalleryService,
  IArticleInfoBox,
  MindmegetteAdultComponent,
  MindmegetteArticleCardType,
  MindmegetteNewsletterCardType,
  MindmegetteQuizComponent,
  MindmegetteVotingComponent,
  MindmegetteWysiwygBoxComponent,
  MMESliderGalleryComponent,
  NewsletterComponent,
  RecipeCardType,
  RssFeedService,
  SecretDaysCalendarAdapterComponent,
  SliderGalleryFullscreenLayerClickedEvent,
  SponsoredTagBoxComponent,
  TagComponent,
  WrapTablePipe,
} from '../../../shared';

import { DomSanitizer, SafeHtml } from '@angular/platform-browser';
import { LinkifyPipe } from '@shared/pipes/linkify.pipe';
import { shouldShowDesktopAdArticle } from '@shared/utils/advert.utils';
import { format } from 'date-fns';
import { hu } from 'date-fns/locale';
import { SocialCounts } from '../../comment-section/api/user-comments.definitions';
import { CommentSectionComponent } from '../../comment-section/components/comment-section/comment-section/comment-section.component';
import { SidebarComponent } from '../../layout/components/sidebar/sidebar.component';
import { ArticlePageResolverData } from '../api/article-page.resolver';
import { ArticleHeaderComponent } from './article-header/article-header.component';
import { RecommendedArticlesComponent } from './recommended-articles/recommended-articles.component';
import { MindmegetteQuizEnum } from '@shared/components/quiz/definitions/mindmegette-quiz.definitions';
import { BreadcrumbComponent } from '@shared/components/breadcrumb/breadcrumb.component';

@Component({
  selector: 'app-article-page',
  templateUrl: 'article-page.component.html',
  styleUrls: ['article-page.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [AutoArticleBodyAdService, FormatDatePipe],
  imports: [
    NgIf,
    ArticleHeaderComponent,
    AdvertisementAdoceanComponent,
    ArticleSocialShareComponent,
    NgTemplateOutlet,
    NativtereloComponent,
    RecommendedArticlesComponent,
    ExternalRecommendationsComponent,
    CommentSectionComponent,
    GoogleNewsComponent,
    SidebarComponent,
    NgFor,
    NgSwitch,
    NgSwitchCase,
    ArticleFileLinkDirective,
    NgClass,
    RouterLink,
    FocusPointDirective,
    ArticleVideoComponent,
    ArticleInfoBoxComponent,
    SlicePipe,
    TitleCasePipe,
    FormatDatePipe,
    WrapTablePipe,
    MindmegetteAdultComponent,
    TagComponent,
    MindmegetteWysiwygBoxComponent,
    ArticleCardComponent,
    MindmegetteVotingComponent,
    MMESliderGalleryComponent,
    CategoryLabelComponent,
    NewsletterComponent,
    LinkifyPipe,
    BreadcrumbComponent,
    SponsoredTagBoxComponent,
    SecretDaysCalendarAdapterComponent,
    MindmegetteQuizComponent,
  ],
})
export class ArticlePageComponent implements OnInit, AfterViewInit, OnDestroy {
  @ViewChild('dataTrigger') dataTrigger: ElementRef<HTMLDivElement>;

  article?: Article & { breadcrumbTitle?: string };
  articleSlug?: string;
  articleUrl: string[] = [];
  articleRecommendations?: ArticleCard[];
  freshArticles: ArticleCard[];
  isUserAdultChoice?: boolean;

  adverts?: ArticleAdvertisements;
  box_1_tablet?: Advertisement;
  box_2_tablet?: Advertisement;
  mobilrectangle_ottboxextra_tablet?: Advertisement;
  metaData?: IMetaData;
  adPageType = PAGE_TYPES.article_pages;
  galleries: Record<string, GalleryData> = {};
  socialCount: ArticleSocial = {};
  isExceptionAdvertEnabled: boolean;
  sponsoredTag?: SponsoredTag;
  readonly ArticleBodyType = { ...ArticleBodyType, ...{ RossmannQuiz: 'ContentGroup.RossmannQuiz' } };
  readonly ArticleCardType = MindmegetteArticleCardType;
  readonly RecipeCardType = RecipeCardType;
  readonly CategoryLabelType = CategoryLabelTypes;
  readonly #unsubscribe$: Subject<boolean> = new Subject();
  readonly MindmegetteNewsletterCardType = MindmegetteNewsletterCardType;
  readonly MinuteToMinuteState = MinuteToMinuteState;
  readonly MindmegetteQuizEnum = MindmegetteQuizEnum;
  embedPrAdvert?: SafeHtml;
  showDesktopAdLocal: boolean;

  #cannonicalUrl?: string;
  replaceableAds = SecondaryFilterAdvertType.REPLACEABLE;
  socialInteractionData?: SocialCounts;

  tereloUrl: string =
    'https://terelo.mediaworks.hu/nativterelo/nativterelo.html?utmSource=mindmegette.hu' +
    '&traffickingPlatforms=Mindmegette%20Nat%C3%ADv' +
    '&domain=Mindmegette';
  googleNewsData: GoogleNewsData = {
    portalName: 'Mindmegette',
    googleNewsUrl: 'https://news.google.com/publications/CAAqBwgKMM_-swsw3JnLAw?hl=hu&gl=HU&ceid=HU%3Ahu',
  };
  articleMeta?: ApiResponseMeta;

  constructor(
    private readonly route: ActivatedRoute,
    protected readonly router: Router,
    private readonly seo: SeoService,
    private readonly cdr: ChangeDetectorRef,
    private readonly adStore: AdvertisementAdoceanStoreService,
    private readonly storage: StorageService,
    private readonly schemaService: SchemaOrgService,
    private readonly voteService: VoteService,
    private readonly galleryService: GalleryService,
    private readonly utilsService: UtilService,
    private readonly autoArticleBodyAd: AutoArticleBodyAdService,
    private readonly adoMetaService: AdvertisementMetaService,
    private readonly analyticsService: AnalyticsService,
    private readonly sanitizer: DomSanitizer,
    private readonly rssFeedService: RssFeedService,
    private readonly formatDate: FormatDatePipe,
    @Inject(DOCUMENT) private readonly document: Document
  ) {}

  get galleriesData(): Record<string, GalleryData> {
    return this.galleries as unknown as Record<string, GalleryData>;
  }

  ngOnInit(): void {
    (this.route.data as Observable<{ data: ArticlePageResolverData }>).pipe(takeUntil(this.#unsubscribe$)).subscribe(
      ({
        data: {
          article: { data: article, meta },
          articleSlug,
          recommendations,
          socialCount,
          freshArticles,
        },
      }) => {
        this.autoArticleBodyAd.init(article.body);
        const body = this.autoArticleBodyAd.autoAd();
        this.articleMeta = meta;
        this.sponsoredTag = this.articleMeta?.['sponsoredTag'];

        this.article = {
          ...article,
          slug: articleSlug,
          body: this.#prepareArticleBody(body),
          excerpt: article?.lead || article?.excerpt,
          tags: article?.tags
            ? article.tags.map((tag) => {
                return { ...tag, path: 'cimke' };
              })
            : [],
        };
        this.freshArticles = freshArticles;
        this.embedPrAdvert = this.sanitizer.bypassSecurityTrustHtml(this.article?.embedPrAdvert ?? '');
        this.articleRecommendations = recommendations?.data?.articlesByTags;

        this.articleSlug = articleSlug;
        this.articleUrl = buildArticleUrl(this.article);
        this.socialCount = socialCount;
        this.isUserAdultChoice = (this.storage.getSessionStorageData('isAdultChoice', false) ?? false) && this.article?.isAdultsOnly;
        const urlWithBackendData = `${this.article.columnSlug}/${this.article?.slug}`;
        this.#cannonicalUrl = this.article.seo?.seoCanonicalUrl || this.article?.canonicalUrl || `${this.seo.hostUrl}/${urlWithBackendData || ''}`;
        this.socialInteractionData = {
          like: this.article.likeCount,
          dislike: this.article.dislikeCount,
          comment: this.article.commentCount,
          readingTime: this.article.readingTime,
          areCommentsHidden: (this.article as ArticleCardWithSocial).isCommentsDisabled,
          areReactionsHidden: (this.article as ArticleCardWithSocial).isLikesAndDislikesDisabled,
        };
        this.adStore.setIsAdultPage(this.isUserAdultChoice);
        this.adStore.setArticleParentCategory(this.adPageType);
        this.seo.updateCanonicalUrl(this.#cannonicalUrl ?? '', { addHostUrl: false, skipSeoMetaCheck: true });
        this.loadEmbeddedGalleries();

        // Set RSS Feed
        this.rssFeedService.removeRssFeed(true);
        this.rssFeedService.addRssFeed(false, this.article.columnSlug, this.article.columnTitle);

        this.#setMetaData();
        if (this.article) {
          this.schemaService.removeStructuredData();
          this.schemaService.insertSchema(
            createWebPageSchema({
              url: this.seo.hostUrl,
              primaryImageOfPage: getSchemaImageObject(this.article.thumbnail ?? `${this.seo.hostUrl}/assets/images/placeholder.jpg`),
            })
          );
          this.schemaService.insertSchema({
            ...getStructuredDataForArticle(this.article, this.seo.currentUrl, environment?.siteUrl ?? '', { hasAuthorPageSlug: true }),
          } as SchemaOrg);
          extractVideoSchemasFromWysiwyg(this.article.body, {
            name: `${this.article.title} | Mindmegette.hu`,
            description: this.article.lead,
            uploadDate: this.article.publishDate,
            dateCreated: this.article.publishDate,
            thumbnailUrl: this.article.thumbnail ?? `${this.seo.hostUrl}/assets/images/placeholder.jpg`,
          }).forEach((schema) => this.schemaService.insertSchema(schema));
        }
        this.cdr.markForCheck();

        setTimeout(() => {
          this.analyticsService.sendPageView(
            {
              pageCategory: this.article?.columnSlug as string,
              customDim2: this.article?.topicLevel1,
              customDim1: this.article?.aniCode,
              title: this.article?.title,
              articleSource: this.article?.articleSource ? this.article.articleSource : 'no source',
              publishDate: this.formatDate.transform(this.article?.publishDate as Date, 'dateTime'),
              lastUpdatedDate: this.formatDate.transform(
                (this.article?.lastUpdated ? this.article.lastUpdated : this.article?.publishDate) as Date,
                'dateTime'
              ),
            },
            'Cikk'
          );
        }, 0);
      }
    );

    (
      combineLatest([
        this.route.data as Observable<{
          data: ArticleResolverData;
        }>,
        this.adStore.isAdult.asObservable(),
      ]) as Observable<[{ data: ArticleResolverData }, boolean]>
    )
      .pipe(takeUntil(this.#unsubscribe$))
      .pipe(
        map<[{ data: ArticleResolverData }, boolean], boolean | undefined>(
          ([
            {
              data: { article },
            },
            _isAdult,
          ]) => {
            // RESET ADOCEAN META TAGS
            // ngOnDestroy WILL NOT BE CALLED WHEN WE NAVIGATE TO ANOTHER ARTICLE FROM ARTICLE!!
            const advertMeta = this.adStore.advertMeta$.getValue();
            this.adStore.advertMeta$.next({
              vars: advertMeta.vars,
              keys: '',
            });

            const tagSlugs = article?.data?.tags?.map((tag) => tag?.slug) as string[];
            const secondaryColumnSlugs = article?.data?.secondaryColumns?.map((column) => column?.slug) as string[];
            this.adoMetaService.set(tagSlugs);
            this.adoMetaService.set(secondaryColumnSlugs);
            if (article?.data?.primaryColumn) {
              this.adoMetaService.set([article?.data?.primaryColumn?.slug]);
            }
            this.isExceptionAdvertEnabled = article?.data.isExceptionAdvertEnabled;
            return article?.data?.withoutAds;
          }
        ),

        switchMap((withoutAds) => {
          this.resetAds();
          withoutAds ? this.adStore.disableAds() : this.adStore.enableAds();
          return this.adStore.advertisemenets$;
        })
      )
      .subscribe((adsCollection): void => {
        this.adverts = this.adStore.separateAdsByMedium(
          adsCollection,
          this.adPageType,
          ALL_BANNER_LIST,
          SecondaryFilterAdvertType.REPLACEABLE,
          PAGE_TYPES.other_pages
        );
        this.box_1_tablet = { ...this.adverts?.mobile?.['mobilrectangle_1'], medium: 'desktop' };
        this.box_2_tablet = { ...this.adverts?.mobile?.['mobilrectangle_2'], medium: 'desktop' };
        this.mobilrectangle_ottboxextra_tablet = {
          ...this.adverts?.mobile?.['mobilrectangle_ottboxextra'],
          medium: 'desktop',
        };
        this.showDesktopAdLocal = shouldShowDesktopAdArticle(this.utilsService.isBrowser());
        this.adStore.onArticleLoaded();
        this.cdr.detectChanges();
      });
  }

  private resetAds(): void {
    this.adverts = undefined;
    this.cdr.detectChanges();
  }

  ngAfterViewInit(): void {
    if (this.utilsService.isBrowser()) {
      setTimeout(() => {
        this.route.data.pipe(takeUntil(this.#unsubscribe$)).subscribe(() => {
          if ('IntersectionObserver' in window) {
            this.observeArticleEnd();
          }
        });
      }, 1000);
    }

    this.route.fragment.pipe(takeUntil(this.#unsubscribe$)).subscribe((fragment) => {
      // Scrolling is only possible in the browser.
      if (this.utilsService.isBrowser() && fragment) {
        const elem = this.document.getElementById(fragment);
        if (elem) {
          window.requestAnimationFrame(() => {
            elem.scrollIntoView({ block: 'start' });
          });
        }
      }
    });
  }

  ngOnDestroy(): void {
    this.adStore.onArticleDestroy();
    this.#unsubscribe$.next(true);
    this.#unsubscribe$.complete();
  }

  getMinuteToMinuteDate(date: string): string {
    return format(new Date(date), 'MMMM dd, yyyy', { locale: hu });
  }

  getVoteData(value: VoteData): VoteDataWithAnswer {
    return this.voteService.getVoteData(value);
  }

  simpleArticleRecommendation(articleBodyDetail: ArticleBodyDetails): ArticleCard {
    const article = articleBodyDetail.value;
    return {
      ...article,
      columnSlug: article?.category?.slug,
      columnTitle: article?.category?.name,
    } as ArticleCard;
  }

  getInfoBoxData(details: ArticleBodyDetails[]): IArticleInfoBox {
    return {
      title: details[0].value,
      description: details[1].value,
      type: details[2].value.value,
    };
  }

  onIsUserAdultChoose(isUserAdult: boolean): void {
    this.isUserAdultChoice = isUserAdult;
    this.adStore.setIsAdultPage(isUserAdult);
  }

  private loadEmbeddedGalleries(): void {
    const bodyElements = (this.article?.body as any) ?? [];
    const gallerySubs = ((bodyElements ?? []) as GalleryElementData[])
      .filter(({ type }) => type === ArticleBodyType.Gallery)
      .filter((bodyElem) => !!bodyElem.details[0].value)
      .map((bodyElem: GalleryElementData) => this.galleryService.getGalleryDetails(bodyElem.details[0].value.slug));

    forkJoin(gallerySubs)
      .pipe(takeUntil(this.#unsubscribe$))
      .subscribe((galleries) => {
        galleries.forEach((gallery) => {
          this.galleries[gallery.id] = {
            ...gallery,
            highlightedImageUrl: gallery.highlightedImage.url,
          } as GalleryData;
          this.cdr.markForCheck();
        });
      });
  }

  #prepareArticleBody(body: ArticleBody[]): ArticleBody[] {
    let advertIndex = 1;
    return body.map((bodyPart: ArticleBody) => ({
      ...bodyPart,
      details: (bodyPart.details ?? []).map((detail: ArticleBodyDetails) => ({
        ...detail,
        ...this.#prepareArticleBodyDetail(detail, bodyPart.type),
      })),
      ...(bodyPart.type === ArticleBodyType.Advert && {
        adverts: {
          mobile: `mobilinterrupter_${advertIndex}`,
          desktop: `desktopinterrupter_${advertIndex++}`,
        },
      }),
    }));
  }

  #prepareArticleBodyDetail(detail: ArticleBodyDetails, type: ArticleBodyType): ArticleBodyDetails {
    if (type === ArticleBodyType.Article) {
      return {
        ...detail,
        value: previewBackendArticleToArticleCard(detail.value),
      };
    }
    return {
      ...detail,
    };
  }

  #setMetaData(): void {
    const { thumbnail, publicAuthor, publishDate, alternativeTitle, metaThumbnail, secondaryThumbnail } = this.article || {};
    if (!this.article) {
      return;
    }

    const title = this.article.seo?.seoTitle || this.article.title;
    const comboTitle = `${title} | ${defaultMetaInfo?.ogSiteName}`;
    const finalTitle = alternativeTitle ?? comboTitle;
    const finalOgTitle = alternativeTitle && alternativeTitle.length > 0 ? alternativeTitle : `${this.article.title} - ${defaultMetaInfo?.ogSiteName}`;

    this.metaData = {
      ...defaultMetaInfo,
      title: finalTitle,
      description: this.article.seo?.seoDescription || this.article.excerpt || this.article.lead || defaultMetaInfo.description,
      robots: this.article.seo?.seoRobotsMeta || 'index, follow, max-image-preview:large',
      ogTitle: finalOgTitle,
      ogImage: secondaryThumbnail || metaThumbnail || thumbnail,
      ogType: 'article',
      articleAuthor: publicAuthor,
      articlePublishedTime: publishDate?.toISOString(),
      articleModifiedTime: this.article?.lastUpdated?.toISOString(),
      author: this.article?.publicAuthor ?? '',
    };
    this.seo.setMetaData(this.metaData, { skipSeoMetaCheck: true });
  }

  handleCommentSubmitted(): void {
    this.socialInteractionData = {
      ...this.socialInteractionData,
      comment: (this.socialInteractionData?.comment ?? 0) + 1,
    };
  }

  refreshMinuteToMinute(): void {
    if (this.utilsService.isBrowser()) {
      this.document.defaultView?.location.reload();
    }
  }

  /**
   * When changing slides in the gallery we should send a pageView to Google Analytics and Gemius.
   * We need to explicitly send the href, title and referrers as these pageViews are just "virtual" views, because
   * they are not triggered by a real navigation in the browser.
   * @param gallery gallery that should receive the page views.
   * @param params parameters of the slide change event. For example the index of the image
   */
  handleGallerySlideChange(gallery: GalleryData, params: any): void {
    const { index } = params;
    const galleryUrl = [this.seo.hostUrl, 'galeria', gallery.slug, ...(index ? [index + 1] : [])].join('/');
    const pageViewParams = {
      href: galleryUrl,
      title: gallery.title,
      referrer: this.seo.currentUrl,
    } as any;
    this.analyticsService.sendPageView(pageViewParams, 'Galéria');
    if (typeof pp_gemius_hit !== 'undefined') {
      pp_gemius_hit(environment.gemiusId, `page=${galleryUrl}`);
    }
  }

  openGalleryDedicatedRouteLayer({ gallery, selectedImageIndex }: SliderGalleryFullscreenLayerClickedEvent): void {
    if (!gallery || !this.utilsService.isBrowser()) {
      return;
    }

    const url = location.pathname;
    const galleryUrl = ['/', 'galeria', gallery.slug, ...(selectedImageIndex || selectedImageIndex === 0 ? [selectedImageIndex + 1] : [])];

    this.router.navigate(galleryUrl, { state: { referrerArticle: url } });
  }

  private observeArticleEnd(): void {
    if (!this.dataTrigger?.nativeElement) return;

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(({ isIntersecting }) => {
        if (isIntersecting) {
          this.sendEcommerceEvent();
          if (this.dataTrigger) {
            observer.unobserve(this.dataTrigger.nativeElement);
          }
        }
      });
    });
    observer.observe(this.dataTrigger.nativeElement);
  }

  private sendEcommerceEvent(): void {
    const routeParams: ArticleRouteParams = this.route.snapshot.params as ArticleRouteParams;
    if (!this.article) return;

    this.analyticsService.sendEcommerceEvent({
      id: `T${this.article.id}`,
      title: this.article.title,
      articleSlug: routeParams.articleSlug ? routeParams.articleSlug : 'cikk-elonezet',
      category: this.article.columnTitle ?? '',
      articleSource: this.article.articleSource ? this.article.articleSource : 'no source',
      publishDate: this.formatDate.transform(this.article.publishDate as Date, 'dateTime'),
      lastUpdatedDate: this.formatDate.transform((this.article.lastUpdated ? this.article.lastUpdated : this.article.publishDate) as Date, 'dateTime'),
    });
  }
}
