<section *ngIf="publicUser">
  <div class="wrapper profile">
    <div class="profile-left-column">
      <div class="profile-centered">
        <mindmegette-avatar-initials
          *ngIf="publicUser?.userName"
          [userName]="publicUser.userName!"
          [style]="MindmegetteNameInitialType.PROFILE"
          [size]="isDesktopView ? 160 : 120"
          [avatarImgUrl]="publicUser.avatarImage"
        >
        </mindmegette-avatar-initials>
        <h1 class="profile-name">{{ publicUser?.userName }}</h1>
        <div class="profile-details">
          <div class="profile-details-wrapper">
            <div class="profile-item">
              <span>{{ publicUser?.recipeCount || 0 }}</span>
              recept
            </div>
            <div class="profile-separator"></div>
            <div class="profile-item">
              <span>{{ publicUser?.follower || 0 }}</span>
              követő
            </div>
            <div class="profile-separator"></div>
            <div class="profile-item">
              <span>{{ publicUser?.followed || 0 }}</span>
              követés
            </div>
          </div>
          <div class="profile-member-since">{{ publicUser?.membership }} óta tag</div>
        </div>
        <app-profile-badges *ngIf="publicUser?.badges" [badgeData]="publicUser?.badges!" [isCentered]="true"></app-profile-badges>
        <mindmegette-simple-button
          (click)="onFollowPublicUser()"
          *ngIf="!isFollowed && !isMyProfile"
          [wide]="true"
          class="subscribe-button"
          color="primary"
          icon="subscribe"
          iconPosition="left"
        >
          Bekövetés
        </mindmegette-simple-button>
        <mindmegette-simple-button (click)="unfollowUserProfile()" *ngIf="isFollowed && !isMyProfile" [wide]="true" class="subscribe-button" color="outline">
          Követés leállítása
        </mindmegette-simple-button>
      </div>
      <div class="profile-introduction" *ngIf="publicUser?.additionalDetails?.introduction as introduction">
        <div class="profile-separator horizontal"></div>
        <h3 class="profile-introduction-title">Rólam</h3>
        <p class="profile-introduction-text">{{ introduction }}</p>
        <h4 class="profile-introduction-social">Social / weboldal</h4>
        <div class="profile-social">
          <a *ngIf="publicUser?.additionalDetails?.facebook" [href]="publicUser?.additionalDetails?.facebook" target="_blank">
            <i class="icon mindmegette-icon-facebook"></i>
          </a>
          <a *ngIf="publicUser?.additionalDetails?.instagram" [href]="publicUser?.additionalDetails?.instagram" target="_blank">
            <i class="icon icon-instagram-transparent"></i>
          </a>
          <a *ngIf="publicUser?.additionalDetails?.website" [href]="publicUser?.additionalDetails?.website" target="_blank">
            <i class="icon icon-website"></i>
          </a>
        </div>
      </div>
      <ng-container *ngIf="hasAnyFavoriteData">
        <div class="profile-separator horizontal"></div>
        <strong class="profile-personal-title">Kedvenc</strong>
        <div class="profile-personal-data" *ngIf="publicUser?.additionalDetails?.favoriteMeal as favoriteMeal"><strong>Étel:</strong> {{ favoriteMeal }}</div>
        <div class="profile-personal-data" *ngIf="publicUser?.additionalDetails?.favoriteChef as favoriteChef"><strong>Séf:</strong> {{ favoriteChef }}</div>
        <div class="profile-personal-data" *ngIf="publicUser?.additionalDetails?.favoriteSpice as favoriteSpice">
          <strong>Fűszer:</strong> {{ favoriteSpice }}
        </div>
        <div class="profile-personal-data" *ngIf="publicUser?.additionalDetails?.favoriteIngredient as favoriteIngredient">
          <strong>Hozzávaló:</strong> {{ favoriteIngredient }}
        </div>
      </ng-container>
      <ng-container *ngIf="hasAnyGreatestData">
        <div class="profile-separator horizontal"></div>
        <strong class="profile-personal-title">Legnagyobb</strong>
        <div class="profile-personal-data" *ngIf="publicUser?.additionalDetails?.greatestSuccess as greatestSuccess">
          <strong>Siker:</strong> {{ greatestSuccess }}
        </div>
        <div class="profile-personal-data" *ngIf="publicUser?.additionalDetails?.greatestFailure as greatestFailure">
          <strong>Kudarc:</strong> {{ greatestFailure }}
        </div>
      </ng-container>
    </div>
    <div class="profile-right-column">
      <app-profile-own-recipes title="Feltöltött receptek" [setMeta]="false" [showPageInTitle]="true"></app-profile-own-recipes>
    </div>
  </div>
</section>
