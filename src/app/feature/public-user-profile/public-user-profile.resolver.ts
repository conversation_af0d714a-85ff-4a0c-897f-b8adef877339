import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { forkJoin, Observable, of, throwError } from 'rxjs';
import { PublicUserProfileDetails, PublicUserProfileResponse } from './public-user-profile.definitions';
import { catchError, map, tap } from 'rxjs/operators';
import { HttpErrorResponse } from '@angular/common/http';
import { ApiService, ProfileOwnRecipesService } from '../../shared';

@Injectable({
  providedIn: 'root',
})
export class PublicUserProfileResolver {
  constructor(
    private readonly apiService: ApiService,
    private readonly router: Router,
    private readonly ownRecipesService: ProfileOwnRecipesService
  ) {}

  resolve(route: ActivatedRouteSnapshot): Observable<PublicUserProfileResponse> {
    const userId = (route.parent?.params as { userId?: string })?.userId || '';
    const { page, sort, global_filter } = route.queryParams;
    const params = this.ownRecipesService.getHttpParams(page, sort, global_filter, '24');
    return forkJoin({
      publicUserID: of(userId),
      publicUser: this.apiService.getUserProfileDetails(userId).pipe(
        map(({ data }) => {
          return {
            ...data,
            userName: data?.userName,
          } as PublicUserProfileDetails;
        })
      ),
      uploadedRecipes: this.apiService.getUserUploadedRecipes(userId, params).pipe(
        tap(({ meta }) => {
          if ((meta.limitable?.pageMax ?? 0) < page - 1) {
            throw new Error('Page limit out of bounds');
          }
        }),
        map((data) => {
          this.ownRecipesService.next(data);
          return data;
        })
      ),
    }).pipe(
      catchError((error: HttpErrorResponse) => {
        this.router
          .navigate(['/', '404'], {
            state: { errorResponse: JSON.stringify(error) },
            skipLocationChange: false,
          })
          .then();
        return throwError(() => error);
      })
    );
  }
}
