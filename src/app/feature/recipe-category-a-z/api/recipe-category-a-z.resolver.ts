import { inject } from '@angular/core';
import type { ResolveFn } from '@angular/router';
import { ApiService } from '@shared/services';
import { map } from 'rxjs/operators';
import { BackendRecipeCategorySearchResponse } from '@shared/definitions';

export const hungarianAlphabet: Readonly<Record<string, BackendRecipeCategorySearchResponse[]>> = {
  a: [],
  á: [],
  b: [],
  c: [],
  cs: [],
  d: [],
  dz: [],
  dzs: [],
  e: [],
  é: [],
  f: [],
  g: [],
  gy: [],
  h: [],
  i: [],
  í: [],
  j: [],
  k: [],
  l: [],
  ly: [],
  m: [],
  n: [],
  ny: [],
  o: [],
  ó: [],
  ö: [],
  ő: [],
  p: [],
  q: [],
  r: [],
  s: [],
  sz: [],
  t: [],
  ty: [],
  u: [],
  ú: [],
  ü: [],
  ű: [],
  v: [],
  w: [],
  x: [],
  y: [],
  z: [],
  zs: [],
};

function initAlphabet(): typeof hungarianAlphabet {
  return Object.fromEntries(Object.keys(hungarianAlphabet).map((key) => [key, []]));
}

export const recipeCategoryAZResolver: ResolveFn<Record<string, BackendRecipeCategorySearchResponse[]>> = () => {
  const api = inject(ApiService);
  return api.getRecipeCategories().pipe(map(({ data }) => assignItems(data)));
};
function assignItems(items: BackendRecipeCategorySearchResponse[]): Record<string, BackendRecipeCategorySearchResponse[]> {
  // Sort Hungarian letters by length DESC, so "dzs" matches before "d"
  const alphabet = initAlphabet();
  const letters = Object.keys(alphabet).sort((a, b) => b.length - a.length);

  items.forEach((item) => {
    if (!item.title) {
      return;
    }
    const lowerTitle = item.title.toLowerCase();

    // find the first matching Hungarian "letter"
    const letter = letters.find((l) => lowerTitle.startsWith(l));

    if (letter) {
      alphabet[letter].push(item);
    }
  });

  return alphabet;
}
