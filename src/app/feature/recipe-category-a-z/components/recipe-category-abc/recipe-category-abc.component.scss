@use 'shared' as *;

:host {
  display: block;
  background-color: var(--kui-white);
  border: 1px solid var(--kui-gray-100);
  border-radius: 8px;
  padding: 20px 16px;

  .kesma-swipe {
    ::ng-deep {
      .item {
        width: 36px;
      }
    }
  }

  .wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }

  .title {
    font-size: 24px;
    font-weight: 600;
    line-height: 28px;
    letter-spacing: 0.12px;
    font-family: var(--kui-font-primary);
  }

  .letter {
    width: 100%;
    cursor: pointer;
    text-transform: uppercase;
    font-family: var(--kui-font-secondary);
    color: var(--kui-gray-400);
    font-size: 18px;
    font-weight: 500;
    line-height: 26px;
    letter-spacing: 0.18px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;

    &.unavailable {
      pointer-events: none;
      cursor: not-allowed;
      opacity: 0.4;
    }

    &.active,
    &:hover {
      color: var(--kui-gray-950);
      background-color: var(--kui-green-100);
    }
  }

  .navigation {
    display: flex;
    gap: 12px;

    @include media-breakpoint-down(sm) {
      display: none;
    }

    .icon-wrapper {
      width: 28px;
      height: 28px;
      background-color: var(--kui-green-700);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
    }
  }

  .mindmegette-icon-white-right-arrow,
  .mindmegette-icon-white-left-arrow {
    width: 20px;
    height: 20px;
  }
}
