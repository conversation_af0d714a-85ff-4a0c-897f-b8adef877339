<div class="wrapper">
  <div class="title">Receptkeresés kezdőbetű alapján:</div>

  <div class="navigation">
    <button class="icon-wrapper" (click)="swipePrev()">
      <i class="icon mindmegette-icon-white-left-arrow"></i>
    </button>
    <button class="icon-wrapper" (click)="swipeNext()">
      <i class="icon mindmegette-icon-white-right-arrow"></i>
    </button>
  </div>
</div>
<ng-template #itemTemplate let-item="data">
  <a (click)="scrollTo(item.letter, $event)" class="letter" [class.unavailable]="!item.used" [href]="currentUrl() + '#kategoria-' + item.letter">
    {{ item.letter }}
  </a>
</ng-template>
<div
  kesma-swipe
  [itemTemplate]="itemTemplate"
  [data]="letterUsage()"
  [dataTrackByProperty]="null"
  [breakpoints]="{
    default: {
      itemCount: 'auto',
      pageSize: 'auto',
      gap: '8px',
    },
  }"
></div>
