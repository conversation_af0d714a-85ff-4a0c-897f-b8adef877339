@use 'shared' as *;

.recipe-category-az-page {
  margin: 20px 16px 32px;
  font-family: var(--kui-font-primary);
  color: var(--kui-gray-950);

  @include media-breakpoint-up(md) {
    margin: 28px 32px 92px;
  }

  .title {
    margin-bottom: 32px;
    font-size: 24px;
    font-weight: 600;
    line-height: 28px;

    @include media-breakpoint-up(md) {
      font-size: 34px;
      font-weight: 600;
      line-height: 40px;
    }
  }
  .search {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    &-bar {
      width: 100%;
      display: flex;
      align-items: center;
      gap: 10px;
      background-color: var(--kui-white);
      border: 1px solid var(--kui-gray-100);
      border-radius: 8px;
      &-icon {
        width: 16px;
        height: 16px;
        margin-left: 12px;
      }
      input {
        width: 100%;
        height: 100%;
        background-color: transparent;
      }
    }
  }
  .letter {
    margin-block: 20px;
    scroll-margin-top: 1500px;
    scroll-padding-top: 40px;
    h3 {
      background-color: var(--kui-green-700);
      padding: 10px 16px;
      border-radius: 8px;
      color: var(--kui-white);
      text-transform: uppercase;
      display: inline-block;
      font-size: 20px;
      margin-bottom: 20px;
    }
    .items {
      display: grid;
      grid-template-columns: repeat(1, 1fr);
      gap: 0 15px;
      @include media-breakpoint-up(sm) {
        grid-template-columns: repeat(2, 1fr);
      }
      @include media-breakpoint-up(md) {
        grid-template-columns: repeat(3, 1fr);
      }
      @include media-breakpoint-up(lg) {
        grid-template-columns: repeat(4, 1fr);
      }
      a {
        border-top: 1px solid var(--kui-gray-200);
        color: var(--kui-black);
        padding-top: 15px;
        padding-bottom: 15px;
        &:hover {
          opacity: 0.7;
        }
      }
    }
  }
}
