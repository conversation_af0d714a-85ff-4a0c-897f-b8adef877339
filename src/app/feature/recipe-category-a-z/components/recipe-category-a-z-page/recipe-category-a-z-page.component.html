<section class="recipe-category-az-page">
  <div class="wrapper">
    <div class="heading-line">
      <h1 class="title">Receptek A-Z</h1>
    </div>
    <div class="search">
      <form class="search-bar" (submit)="handleSearch()">
        <img class="search-bar-icon" [src]="'/assets/images/icons/magnifier-icon.svg'" alt="" />
        <input [(ngModel)]="searchTerm" [ngModelOptions]="{ standalone: true }" class="header-search-bar-input" type="text" placeholder="Keresés" />
      </form>
      <mindmegette-simple-button (click)="handleSearch()" class="search-button">Keresés</mindmegette-simple-button>
    </div>
    <app-recipe-category-abc [letterUsage]="letterUsage()" />
    @for (entry of categories(); track entry.letter) {
      @if (entry.categories.length) {
        <section [id]="'kategoria-' + entry.letter" class="letter">
          <h2>{{ entry.letter }}</h2>
          <div class="items">
            @for (category of entry.categories; track category.slug) {
              <a [routerLink]="['/receptkategoria', category.slug]">{{ category.title }}</a>
            }
          </div>
        </section>
      }
    }
  </div>
</section>
