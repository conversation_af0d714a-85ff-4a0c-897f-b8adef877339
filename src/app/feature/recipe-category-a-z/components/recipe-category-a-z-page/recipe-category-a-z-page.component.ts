import { ChangeDetectionStrategy, Component, computed, inject, OnInit } from '@angular/core';
import { BackendRecipeCategorySearchResponse, createMMETitle, defaultMetaInfo, MindmegetteSimpleButtonComponent } from '../../../../shared';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { toSignal } from '@angular/core/rxjs-interop';
import { RecipeCategoryAbcComponent } from '@feature/recipe-category-a-z/components/recipe-category-abc/recipe-category-abc.component';
import { LetterUsage } from '@feature/recipe-category-a-z/api/recipe-category-a-z.definitions';
import { SeoService } from '@trendency/kesma-core';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'app-recipe-category-a-z-page',
  templateUrl: './recipe-category-a-z-page.component.html',
  styleUrls: ['./recipe-category-a-z-page.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [RecipeCategoryAbcComponent, RouterLink, FormsModule, MindmegetteSimpleButtonComponent],
})
export class RecipeCategoryAZPageComponent implements OnInit {
  private readonly seo = inject(SeoService);
  private readonly activatedRoute = inject(ActivatedRoute);
  private readonly router = inject(Router);
  routeData = toSignal(this.activatedRoute.data);
  categories = computed(() => this.mapCategories(this.routeData()?.['data']));
  letterUsage = computed(() => this.mapToArray(this.routeData()?.['data']));
  searchTerm: string = '';

  ngOnInit(): void {
    this.setMetaData();
  }

  mapToArray(obj: Record<string, BackendRecipeCategorySearchResponse[]>): LetterUsage[] {
    return Object.entries(obj).map(([key, value]) => ({
      letter: key,
      used: value.length > 0,
    }));
  }

  mapCategories(obj: Record<string, BackendRecipeCategorySearchResponse[]>): { letter: string; categories: BackendRecipeCategorySearchResponse[] }[] {
    return Object.entries(obj).map(([key, value]) => ({
      letter: key,
      categories: value.map((category) => ({ ...category, title: this.capitalizeFirst(category.title || '') })),
    }));
  }
  capitalizeFirst(str: string): string {
    if (!str) return '';
    return str.charAt(0).toUpperCase() + str.slice(1);
  }
  handleSearch(): void {
    this.router.navigate(['/kereses'], { queryParams: { global_filter: this.searchTerm, 'content_types[]': 'recipe' } });
  }
  private setMetaData(): void {
    const canonical = 'receptek-a-z';
    this.seo.updateCanonicalUrl(canonical);
    this.seo.setMetaData({
      ...defaultMetaInfo,
      title: createMMETitle('Receptek A-Z'),
      ogTitle: 'Receptek A-Z',
      description: 'Receptek ABC-je a Mindmegette oldalán. Több tízezer bevált magyaros, házias, egészséges, egyszerű, gyors recept.',
      ogDescription: ': Receptek ABC-je a Mindmegette oldalán. Több tízezer bevált magyaros, házias, egészséges, egyszerű, gyors recept.',
      robots: 'index, follow',
    });
  }
}
