@use 'shared' as *;

:host {
  display: block;
  margin: 30px 0;

  .sponsor-stripe {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 32px;
    padding: 24px;
    border-radius: 8px;
    width: 1384px;
    margin: 0 auto 32px;
    max-width: calc(100% - 30px);
    font-family: var(--kui-font-secondary);
    font-size: 20px;
    font-weight: 700;
    line-height: 26px;

    @include media-breakpoint-down(md) {
      padding: 12px 16px;
      gap: 16px;
      font-size: 16px;
      line-height: 22px;
      letter-spacing: 0.16px;
    }

    img {
      height: 60px;
      width: auto;

      @include media-breakpoint-down(md) {
        height: 40px;
      }
    }
  }

  .wrapper {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    gap: 32px;
    @include media-breakpoint-down(md) {
      flex-direction: column;
    }

    main {
      width: 100%;

      .featured-image {
        &-img {
          width: 100%;
          height: auto;
          object-fit: cover;
          aspect-ratio: 2/1;
          border-radius: 8px;
        }

        &-creator {
          margin-top: 8px;
          color: var(--kui-gray-400);
          font-family: var(--kui-font-primary);
          font-size: 12px;
          font-style: normal;
          font-weight: 500;
          line-height: 16px; /* 133.333% */
          letter-spacing: 0.48px;
          text-transform: uppercase;
        }
      }

      .title {
        color: var(--kui-gray-950);
        margin-top: 16px;
        margin-bottom: 12px;
        font-size: 40px;
        font-style: normal;
        font-weight: 600;
        line-height: 48px; /* 120% */
        letter-spacing: -0.4px;
        hyphens: auto;

        @include media-breakpoint-down(sm) {
          font-size: 24px;
          line-height: 28px;
          letter-spacing: 0.12px;
        }
      }

      .lead {
        color: var(--kui-gray-950);
        font-size: 18px;
        font-style: normal;
        font-weight: 500;
        line-height: 26px; /* 144.444% */
        letter-spacing: 0.18px;

        @include media-breakpoint-down(sm) {
          font-size: 16px;
          font-weight: 400;
          line-height: 22px;
          letter-spacing: 0.16px;
        }
      }

      .details {
        margin-top: 32px;
        display: grid;
        grid-template-columns: 1fr 2fr;
        gap: 32px;
        @include media-breakpoint-down(md) {
          display: flex;
          flex-direction: column;
          .details-meta,
          .details-content {
            width: 100%;
            max-width: 100%;
          }
        }

        &-meta {
          width: 100%;
          max-width: 322px;

          &-box {
            @include media-breakpoint-up(md) {
              position: sticky;
              top: 150px;
            }
          }

          &-list {
            display: flex;
            flex-direction: column;
            gap: 11px;
          }
        }

        &-content {
          flex-grow: 1;
          display: flex;
          flex-direction: column;
          gap: 40px;
          overflow: hidden;
          width: 100%;

          h2 {
            color: var(--kui-gray-950);
            font-size: 30px;
            font-style: normal;
            font-weight: 600;
            line-height: 36px; /* 120% */
            letter-spacing: -0.3px;
            margin-bottom: 16px;
          }

          &-part {
            scroll-margin-top: 118px; // Header height scroll margin because of scrollIntoView

            &-share {
              margin-bottom: 24px;
            }

            &-tags {
              display: flex;
              flex-wrap: wrap;
              gap: 2px;
            }

            &-next-occasions {
              display: flex;
              flex-direction: column;
              gap: 24px;
            }

            &-upcoming-experiences {
              display: flex;
              flex-direction: column;
              gap: 24px;
            }
          }
        }
      }
    }

    aside {
      width: 100%;

      @include media-breakpoint-up(lg) {
        max-width: 322px;
      }
    }
  }

  .scroll-target-wrapper {
    position: relative;
    z-index: 1;

    div {
      position: absolute;
      top: -120px;
    }
  }
}

app-gastro-experience-occasion-info-box {
  margin-top: 30px;
}

.order-button {
  margin-top: 24px;

  ::ng-deep {
    button {
      display: flex;
      justify-content: space-between;
    }
  }
}

mindmegette-simple-button.desktop {
  @include media-breakpoint-down(md) {
    display: none;
  }
}

mindmegette-simple-button.mobile {
  display: none;

  @include media-breakpoint-down(md) {
    display: block;
    position: fixed;
    left: 30px;
    right: 30px;
    bottom: 30px;
    width: calc(100% - 60px);
    z-index: 100;
  }
}
