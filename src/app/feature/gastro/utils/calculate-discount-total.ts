import { BackendCoupon } from '@trendency/kesma-ui';

export const calculateDiscountTotal = (total: number, coupon: BackendCoupon | undefined): number | undefined => {
  if (coupon) {
    const discountedTotal = coupon.amount ? total - coupon.amount : Math.round(total * ((100 - coupon.percent) / 100));

    if (discountedTotal <= 0) {
      return 0;
    }

    return discountedTotal;
  }

  return undefined;
};
