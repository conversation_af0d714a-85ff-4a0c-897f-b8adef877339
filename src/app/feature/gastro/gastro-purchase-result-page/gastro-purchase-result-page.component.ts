import { ChangeDetectionStrategy, Component, computed, inject, OnInit, signal, Signal, WritableSignal } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { catchError, map, startWith, switchMap } from 'rxjs/operators';
import { ActivatedRoute, Params, Router, RouterLink } from '@angular/router';
import { GastroPurchaseService } from '../shared/gastro-purchase.service';
import { Observable, of, throwError } from 'rxjs';
import {
  createCanonicalUrlForPageablePage,
  IconComponent,
  SimplePayResponse,
  simplePayResponseCodeMessage,
  SimplePayStatus,
  simplePayStatusMessage,
} from '@trendency/kesma-ui';
import { SeoService, UtilService } from '@trendency/kesma-core';
import { GastroPurchaseStepType } from '../definitions/gastro-purchase.definitions';
import { CommonModule } from '@angular/common';
import {
  createMMETitle,
  defaultMetaInfo,
  MindmegetteSimpleButtonComponent,
  MindmegetteSpinnerComponent,
  SecureApiService,
  UpcomingEventsComponent,
} from '../../../shared';
import { ExperienceRecommendationCard } from '../components/experience-recommendation/definitions/experience-recommendation.definitions';

@Component({
  selector: 'app-gastro-purchase-result-page',
  imports: [RouterLink, CommonModule, UpcomingEventsComponent, MindmegetteSimpleButtonComponent, MindmegetteSpinnerComponent, IconComponent],
  templateUrl: './gastro-purchase-result-page.component.html',
  styleUrl: './gastro-purchase-result-page.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class GastroPurchaseResultPageComponent implements OnInit {
  readonly #route: ActivatedRoute = inject(ActivatedRoute);
  readonly #gastroPurchaseService: GastroPurchaseService = inject(GastroPurchaseService);
  readonly #secureApiService: SecureApiService = inject(SecureApiService);
  readonly #utilService: UtilService = inject(UtilService);
  readonly #router: Router = inject(Router);
  readonly #seo: SeoService = inject(SeoService);

  readonly responseCode: WritableSignal<number | undefined> = signal(undefined);
  readonly transactionId: WritableSignal<number | undefined> = signal(undefined);
  readonly status: WritableSignal<SimplePayStatus | undefined> = signal(undefined);
  readonly SimplePayStatus = SimplePayStatus;

  readonly isError: WritableSignal<boolean> = signal(false);

  readonly simplePayResponseCodeMessage: { [key: number]: string } = simplePayResponseCodeMessage;
  readonly simplePayStatusMessage: { [key: string]: string } = simplePayStatusMessage;

  readonly occasionList: Signal<ExperienceRecommendationCard[]> = toSignal<ExperienceRecommendationCard[]>(
    this.#route.data.pipe(map(({ occasionList }) => occasionList)),
    { requireSync: true }
  );

  readonly isTimeoutStatus: Signal<boolean> = computed(() => this.status() === SimplePayStatus.TIMEOUT);
  readonly isFailStatus: Signal<boolean> = computed(() => this.status() === SimplePayStatus.FAIL);

  readonly isLoading: Signal<boolean> = toSignal<boolean>(
    this.#route.queryParams.pipe(
      switchMap((params: Params) =>
        this.finishPurchase(params).pipe(
          map((isSuccess: boolean) => {
            if (isSuccess) {
              this.#gastroPurchaseService.resetSessionData();
            }

            this.isError.set(!isSuccess);
            return false;
          }),
          startWith(true)
        )
      )
    ),
    { requireSync: true }
  );

  ngOnInit(): void {
    const canonical = createCanonicalUrlForPageablePage('elmenyek/vasarlas');
    if (canonical) this.#seo.updateCanonicalUrl(canonical);
    const title: string = createMMETitle(`Vásárlás befejezése`);
    this.#seo.setMetaData({
      ...defaultMetaInfo,
      title,
      ogTitle: title,
    });
  }

  tryToHandleSimplePayResponse(response: string): false {
    try {
      const base64DecodedResponse: SimplePayResponse = JSON.parse(atob(response));
      this.responseCode.set(base64DecodedResponse?.r);
      this.transactionId.set(base64DecodedResponse?.t);
      this.status.set(base64DecodedResponse?.e);

      if (this.status() === SimplePayStatus.SUCCESS) {
        this.status.set(undefined); // Don't display SimplePay status if success, since backend is not validated signature yet
      }

      return false; // Return false even if status is success, since we need backend validated status
    } catch (e) {
      return false;
    }
  }

  finishPurchase(params: Params): Observable<boolean> {
    // SimplePay response: r = response, s = signature
    if (params['r'] && params['s']) {
      return this.#secureApiService.finishOrderPurchase(params).pipe(
        switchMap(({ event }) => {
          this.tryToHandleSimplePayResponse(params['r']);

          // Use backend validated status instead of SimplePay status
          this.status.set(event);

          return of(this.status() === SimplePayStatus.SUCCESS);
        }),
        catchError((err) => {
          if (this.#utilService.isBrowser()) {
            return of(this.tryToHandleSimplePayResponse(params['r']));
          }

          // If SSR is active, just throw error, so isLoading will remain true, because we can't validate SimplePay response
          // until we get authenticated session which is available only in browser mode
          return throwError(() => err);
        })
      );
    } else {
      return of(false);
    }
  }

  retry(): void {
    this.#gastroPurchaseService.handleStep(GastroPurchaseStepType.SUMMARY);
    this.#router.navigate(['/elmenyek/vasarlas', this.#gastroPurchaseService.sessionData().slug]);
  }
}
