<article *ngIf="data as event" class="experience-recommendation-card">
  @switch (styleId) {
    @case (ExperienceRecommendationCardType.MainImgTitleDateLeadAuthor) {
      <ng-container *ngTemplateOutlet="thumbnailTemplate"></ng-container>
      <ng-container *ngTemplateOutlet="titleTemplate"></ng-container>
      <ng-container *ngTemplateOutlet="publishDateTemplate"></ng-container>
      <ng-container *ngTemplateOutlet="leadTemplate"></ng-container>
      @if (data?.author?.name) {
        <ng-container *ngTemplateOutlet="authorTemplate"></ng-container>
      }
    }
    @case (ExperienceRecommendationCardType.LeftImgTitleDate) {
      <ng-container *ngTemplateOutlet="thumbnailTemplate"></ng-container>
      <div class="experience-recommendation-card-right-column">
        <ng-container *ngTemplateOutlet="titleTemplate"></ng-container>
        <ng-container *ngTemplateOutlet="publishDateTemplate"></ng-container>
      </div>
    }
  }
</article>

<ng-template #thumbnailTemplate>
  <a class="experience-recommendation-card-thumbnail-wrapper" [routerLink]="routerLink" [attr.aria-label]="data?.title">
    <img
      [alt]="data?.thumbnail?.alt || ''"
      [src]="data?.thumbnail?.url || 'assets/images/placeholder.jpg'"
      class="experience-recommendation-card-thumbnail"
      loading="lazy"
    />
  </a>
</ng-template>

<ng-template #titleTemplate>
  <a class="experience-recommendation-card-title-wrapper" [routerLink]="routerLink">
    <h3 class="experience-recommendation-card-title">{{ data?.title }}</h3>
  </a>
</ng-template>

<ng-template #publishDateTemplate>
  <a [routerLink]="routerLink">
    <h4 class="experience-recommendation-card-event-date">{{ eventDate | dfnsFormat: 'yyyy. MMMM dd. HH:mm' : { locale: hu } }}</h4>
  </a>
</ng-template>

<ng-template #leadTemplate>
  <a class="experience-recommendation-card-lead" [routerLink]="routerLink">
    <p class="experience-recommendation-card-lead">{{ data?.lead }}</p>
  </a>
</ng-template>

<ng-template #authorTemplate>
  <a class="experience-recommendation-card-author" [routerLink]="['/', 'szerzo', data?.author?.slug]">
    <img
      [src]="data?.author.avatarUrl || '/assets/images/author-placeholder.svg'"
      [alt]="data?.author?.name + ' szerző képe'"
      class="experience-recommendation-card-author-img"
      loading="lazy"
    />
    <span class="experience-recommendation-card-author-name">{{ data?.author?.name }}</span>
  </a>
</ng-template>
