<div class="occasion-card">
  @switch (styleID) {
    @case (ExperienceOccasionCard.AuthorExperienceOccasion) {
      <ng-container *ngTemplateOutlet="authorBoxOccasionCard"></ng-container>
    }
    @default {
      <ng-container *ngTemplateOutlet="basicOccasionCard"></ng-container>
    }
  }
</div>

<ng-template #basicOccasionCard>
  <ng-container *ngTemplateOutlet="occasionCardPlaces"></ng-container>
  <ng-container *ngTemplateOutlet="occasionCardImage"></ng-container>
  <ng-container *ngTemplateOutlet="occasionCardInfo"></ng-container>
  @if (!hasRemainingNumberOfSeats) {
    <a class="occasion-card-action outline" [routerLink]="['/', 'elmenyek', 'esemenyek', occasion.occasionSlug]"> Értesítést kérek </a>
  } @else if (isComingSoon) {
    <a class="occasion-card-action outline" [routerLink]="['/', 'elmenyek', 'esemenyek', occasion.occasionSlug]" fragment="ertesites"> Értesítést kérek </a>
  } @else {
    <a class="occasion-card-action primary" [routerLink]="['/', 'elmenyek', 'esemenyek', occasion.occasionSlug]" fragment="jelentkezes"> Jelentkezem </a>
  }
</ng-template>

<ng-template #authorBoxOccasionCard>
  <ng-container *ngTemplateOutlet="occasionCardImage"></ng-container>
  <ng-container *ngTemplateOutlet="occasionCardInfo; context: { isAuthorOccasionCard: true }"></ng-container>
  <ng-container *ngTemplateOutlet="occasionCardPlaces; context: { isAuthorOccasionCard: true }"></ng-container>
</ng-template>

<!-- Card templates -->

<ng-template #occasionCardImage>
  <a class="occasion-card-thumbnail" [routerLink]="['/', 'elmenyek', 'esemenyek', occasion.occasionSlug]" [attr.aria-label]="occasion?.experienceTitle">
    <img class="occasion-card-thumbnail" [src]="occasion?.featuredImage || 'assets/images/placeholder.jpg'" alt="Élmény alkalom" loading="lazy" />
  </a>
</ng-template>

<ng-template #occasionCardInfo let-isAuthorOccasionCard="isAuthorOccasionCard">
  <div class="occasion-card-info">
    <a [routerLink]="['/', 'elmenyek', 'esemenyek', occasion.occasionSlug]" [attr.aria-label]="occasion?.experienceTitle">
      <img
        class="occasion-card-thumbnail mobile"
        *ngIf="isAuthorOccasionCard"
        loading="lazy"
        [src]="occasion?.featuredImage || 'assets/images/placeholder.jpg'"
        [alt]="occasion?.experienceTitle ?? ''"
      />
      <h3 class="occasion-card-title">{{ occasion?.experienceTitle }}</h3>
    </a>
    <div class="occasion-card-inner">
      @if (showDate && endDate && occasion?.occasionStartOfExperienceEvent; as startDate) {
        <h4 class="occasion-card-date">{{ startDate | formatDate: 'y-l-d-h-m' }} - {{ endDate ?? '' | formatDate: 'h-m' }}</h4>
      }
      @if (!isOccasionListPage && occasion?.occasionPlace; as place) {
        <span class="occasion-card-place"> {{ place }} </span>
      }
    </div>

    @if (isAuthorOccasionCard) {
      <div class="occasion-card-author-box">
        <img
          loading="lazy"
          [src]="occasion?.publicAuthor?.avatarFullSizeUrl || 'assets/images/author-placeholder.svg'"
          [alt]="occasion?.publicAuthor?.fullName || 'Szerző logó'"
        />
        @if (occasion?.publicAuthor?.fullName; as fullName) {
          <h4>
            {{ fullName }}
            @if (occasion?.publicAuthor?.isMaestroAuthor) {
              <i class="icon mindmegette-icon-verified"></i>
            }
          </h4>
        }
      </div>
    } @else {
      <ng-container *ngTemplateOutlet="occasionCardAuthor"></ng-container>
    }
  </div>
</ng-template>

<ng-template #occasionCardPlaces let-isAuthorOccasionCard="isAuthorOccasionCard">
  @if (!isComingSoon) {
    <div *ngIf="isAuthorOccasionCard" class="occasion-card-divider"></div>
    <div class="occasion-card-places">
      <div class="occasion-card-places-count">{{ occasion?.remainingNumberOfSeats ?? 0 }}</div>
      <h4 class="occasion-card-places-text">Szabad hely maradt</h4>
    </div>
    <div *ngIf="!isAuthorOccasionCard" class="occasion-card-divider"></div>
  } @else {
    <span class="occasion-card-places-text coming-soon">Hamarosan</span>
  }
</ng-template>

<ng-template #occasionCardAuthor>
  @if (occasion?.publicAuthor?.fullName; as authorName) {
    @if (occasion?.publicAuthor?.slug) {
      <a class="occasion-card-author" [routerLink]="['/', 'szerzo', occasion?.publicAuthor.slug]" [attr.aria-label]="authorName">
        <h4>{{ authorName }}</h4>
      </a>
    } @else {
      <h4 class="occasion-card-author">{{ authorName }}</h4>
    }
  }
</ng-template>
