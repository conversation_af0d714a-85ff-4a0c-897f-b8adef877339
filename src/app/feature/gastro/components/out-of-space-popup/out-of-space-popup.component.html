<mindmegette-popup class="popup" (resultEvent)="closeClicked.emit()" [showAcceptButton]="false" [showCancelButton]="false">
  <img class="popup-thumbnail" src="/assets/images/cucumber.svg" alt="" loading="lazy" />
  <h2 class="popup-title">Hoppá - Az aktuális programra pont minden hely elkelt!</h2>
  Sajn<PERSON>ljuk, az esemény ebben a pillanatban megtelt, Kérjük válasz másik időpontot.
  <div class="popup-buttons">
    @if (hasSpaceForAnotherOccasion()) {
      <mindmegette-simple-button (keydown)="anotherDateClicked.emit()" (click)="anotherDateClicked.emit()" [wide]="true" color="primary">
        Másik időpontot nézek
      </mindmegette-simple-button>
      <mindmegette-simple-button (keydown)="notifyMeClicked.emit()" (click)="notifyMeClicked.emit()" [wide]="true" color="outline">
        Értesítést kérek
      </mindmegette-simple-button>
    } @else {
      <mindmegette-simple-button (keydown)="notifyMeClicked.emit()" (click)="notifyMeClicked.emit()" [wide]="true" color="primary">
        Értesítést kérek
      </mindmegette-simple-button>
      <mindmegette-simple-button (keydown)="eventsListClicked.emit()" (click)="eventsListClicked.emit()" [wide]="true" color="outline">
        Tovább az összes eseményhez
      </mindmegette-simple-button>
    }
  </div>
</mindmegette-popup>
