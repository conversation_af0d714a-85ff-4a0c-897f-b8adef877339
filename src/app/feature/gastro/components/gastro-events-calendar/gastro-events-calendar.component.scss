@use 'shared' as *;

:host {
  width: 100%;
  display: block;
}

.events-calendar {
  padding: 16px;
  background: var(--kui-white);
  border-radius: 8px;
  border: 1px solid var(--kui-gray-200);

  .title {
    color: var(--kui-gray-950);
    font-size: 30px;
    font-weight: 600;
    line-height: 36px;
    letter-spacing: -0.3px;
    margin-bottom: 12px;
  }

  .day-selector {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    container-type: inline-size;

    &-day {
      font-family: var(--kui-font-secondary);
      font-size: 20px;
      font-weight: 700;
      line-height: 26px;
    }

    &-buttons {
      display: flex;
      align-items: center;
      gap: 12px;

      @container (max-width: 200px) {
        margin-left: 5px;
      }
    }

    .button-arrow {
      border-radius: 100px;
      border: 1px solid var(--kui-gray-200);
      width: 36px;
      height: 36px;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: background-color 0.3s ease;

      @container (max-width: 200px) {
        width: 28px;
        height: 28px;
      }

      kesma-icon {
        color: #0f1f16;
        transition: 0.3s;
      }

      &.prev kesma-icon {
        transform: rotate(-180deg);
      }

      &:hover {
        background-color: var(--kui-green-700);

        kesma-icon {
          color: var(--kui-white);
        }
      }
    }
  }
}

.listing-divider {
  display: block;
  width: 100%;
  border-bottom: 1px solid var(--kui-gray-200);
  margin-top: 10px;
  margin-bottom: 15px;
}

.events-calendar > .flatpickr-calendar {
  width: 100%;
}

.flatpickr-calendar {
  container-type: inline-size;
  box-shadow: none !important;
  border: none !important;

  .flatpickr-innerContainer {
    .flatpickr-rContainer {
      width: 100%;

      .flatpickr-weekdays {
        margin-bottom: 10px;

        .flatpickr-weekdaycontainer {
          .flatpickr-weekday {
            color: var(--kui-gray-950) !important;
            font-feature-settings: 'salt' on;
            font-family: var(--kui-font-secondary);
            font-size: 14px !important;
            font-weight: 700 !important;
            line-height: 20px !important;
          }
        }
      }

      .flatpickr-days {
        width: 100%;

        .dayContainer {
          width: 100%;
          max-width: none;
          min-width: 0;
          // In some months flatpickr shows a whole week from the next month, but since we don't need it we hide it.
          // However, we need to make sure that the last week of the month is always visible.
          &:has(.nextMonthDay:nth-last-child(n + 7)) {
            // Select all children from .dayContainer IF there are more than 7 children with the class .nextMonthDay
            .flatpickr-day.nextMonthDay:nth-last-child(-n + 7) {
              // Now select the last 7 children with the class .nextMonthDay
              display: none;
            }
          }

          .flatpickr-day {
            width: 100%;
            height: 40px;
            color: var(--kui-gray-950);
            text-align: center;
            font-feature-settings: 'salt' on;
            font-family: var(--kui-font-secondary);
            font-size: 16px;
            font-weight: 500;
            line-height: 24px;
            letter-spacing: 0.16px;
            border-radius: 6px;
            display: flex;
            align-items: center;

            @container (min-width: 300px) {
              max-width: calc((100% - 32px) / 7);
            }

            &:nth-last-child(n + 8) {
              margin-bottom: 10px;
            }

            &:hover {
              background: var(--kui-green-50);
              border: 1px solid var(--kui-green-700);
            }

            &.selected {
              color: var(--kui-white);
              background: var(--kui-green-700);
              border-color: var(--kui-green-700);

              &:hover {
                background: var(--kui-green-50);
                color: var(--kui-black);
              }
            }

            &.event-date {
              border: 1px solid var(--kui-green-700);
            }

            &.today {
              color: var(--kui-gastro-gray-950);
              background: var(--kui-gray-100);
              border: none;
              font-weight: 700;
              line-height: 22px;

              &:hover {
                background: var(--kui-gray-100);
                color: var(--kui-gastro-gray-950);
              }
            }

            &.prevMonthDay,
            &.nextMonthDay {
              color: var(--kui-gray-300);
            }
          }
        }
      }
    }
  }
}
