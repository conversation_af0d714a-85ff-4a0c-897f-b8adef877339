@use 'shared' as *;

@mixin large-checkbox {
  min-height: 24px;
  font-size: 16px;
  font-weight: 400;
  line-height: 22px;
  letter-spacing: 0.16px;

  span {
    margin-left: 10px;
  }

  &:before {
    width: 24px;
    height: 24px;
    top: 0;
  }

  input:checked {
    & + span:before {
      width: 24px;
      height: 24px;
      top: 0;
    }

    & + span:after {
      width: 20px;
      height: 12px;
      background-size: 20px 12px;
      top: 6px;
      left: 2px;
    }
  }

  input.is-invalid {
    & + span:before {
      width: 24px;
      height: 24px;
      top: 0;
    }
  }
}

.purchase {
  &-form-group {
    margin-bottom: 32px;

    @include media-breakpoint-between(lg, lg) {
      margin-bottom: 24px;
    }

    @include media-breakpoint-down(sm) {
      margin-bottom: 24px;
    }

    &.mb-12 {
      margin-bottom: 12px;

      @include media-breakpoint-between(lg, lg) {
        margin-bottom: 8px;
      }

      @include media-breakpoint-down(sm) {
        margin-bottom: 8px;
      }
    }

    &-title {
      color: var(--kui-gray-950);
      font-family: var(--kui-font-secondary);
      font-size: 14px;
      font-weight: 700;
      line-height: 20px;
    }

    &-text {
      color: var(--kui-gray-950);
      font-family: var(--kui-font-secondary);
      font-size: 14px;
      font-weight: 400;
      line-height: 20px;

      &.mt-8 {
        margin-top: 8px;
      }

      &.mb-12 {
        margin-bottom: 12px;
      }

      a {
        color: var(--kui-green-700);
        text-decoration: underline;
        transition: color 0.3s ease;

        &:hover {
          color: var(--kui-green-800);
        }
      }
    }

    .occasion-no-options {
      padding: 16px 0;
      font-family: var(--kui-font-secondary);
      font-size: 14px;
      font-weight: 400;
      line-height: 20px;

      a {
        color: var(--kui-green-700);
        text-decoration: underline;
        transition: color 0.3s ease;

        &:hover {
          color: var(--kui-green-800);
        }
      }
    }

    .occasion-form-options {
      padding: 16px 0;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      gap: 32px;

      .occasion-form-option {
        width: calc(50% - 16px);
        display: flex;
        gap: 16px;
        align-items: flex-start;
        justify-content: flex-start;
        cursor: pointer;

        @include media-breakpoint-between(lg, lg) {
          width: 100%;
        }

        @include media-breakpoint-down(sm) {
          width: 100%;
        }

        &-index {
          color: var(--kui-green-700);
          border: 1px solid var(--kui-green-700);
          font-family: var(--kui-font-secondary);
          font-size: 16px;
          font-weight: 700;
          line-height: 22px;
          letter-spacing: 0.16px;
          width: 28px;
          height: 28px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          transition:
            color 0.3s ease,
            background-color 0.3s ease;
        }

        &-date {
          color: var(--kui-gray-950);
          font-family: var(--kui-font-secondary);
          font-size: 14px;
          font-weight: 400;
          line-height: 20px;
          flex: 1;

          > div:first-child {
            text-transform: uppercase;
          }
        }

        &:hover,
        &.active {
          .occasion-form-option-index {
            background-color: var(--kui-green-700);
            color: var(--kui-white);
          }
        }
      }
    }

    .quantity-form-options {
      padding: 16px 0;
      display: flex;
      flex-wrap: wrap;
      gap: 24px;

      @include media-breakpoint-between(lg, lg) {
        gap: 12px;
      }

      @include media-breakpoint-down(sm) {
        gap: 12px;
      }

      .quantity-form-option {
        background-color: var(--kui-bg);
        border-radius: 8px;
        border: 1px solid var(--kui-gray-100);
        width: calc(25% - 18px);
        cursor: pointer;
        text-align: center;
        color: var(--kui-gray-950);
        font-family: var(--kui-font-secondary);
        font-weight: 400;
        padding: 16px;
        transition: border-color 0.3s ease;

        @include media-breakpoint-between(lg, lg) {
          width: calc(50% - 6px);
        }

        @include media-breakpoint-down(sm) {
          width: calc(50% - 6px);
        }

        &-illustration {
          display: flex;
          justify-content: center;
          margin: 0 auto 8px;

          kesma-icon {
            color: var(--kui-green-700);
            width: 20px;
            height: 20px;

            @include media-breakpoint-between(lg, lg) {
              width: 16px;
              height: 16px;
            }

            @include media-breakpoint-down(sm) {
              width: 16px;
              height: 16px;
            }
          }
        }

        &-quantity {
          font-size: 16px;
          line-height: 22px;
          letter-spacing: 0.16px;
          transition:
            color 0.3s ease,
            font-weight 0.3s ease;

          @include media-breakpoint-between(lg, lg) {
            font-size: 14px;
            line-height: 20px;
            letter-spacing: initial;
          }

          @include media-breakpoint-down(sm) {
            font-size: 14px;
            line-height: 20px;
            letter-spacing: initial;
          }
        }

        &-price {
          font-size: 11px;
          line-height: 14px;
          letter-spacing: 0.11px;
        }

        &:hover,
        &.active {
          border-color: var(--kui-green-700);

          .quantity-form-option-quantity {
            color: var(--kui-green-700);
            font-weight: 700;
          }
        }
      }
    }

    .purchase-gift-select {
      margin-top: 16px;
      display: flex;
      flex-wrap: wrap;
      gap: 32px;

      @include media-breakpoint-between(lg, lg) {
        margin-top: 8px;
        gap: 16px;
      }

      @include media-breakpoint-down(sm) {
        margin-top: 8px;
        gap: 16px;
      }

      > div {
        width: calc(50% - 16px);

        @include media-breakpoint-between(lg, lg) {
          width: 100%;
        }

        @include media-breakpoint-down(sm) {
          width: 100%;
        }
      }

      .mindmegette-form-checkbox {
        color: var(--kui-gray-950);
        margin-bottom: 0;

        span {
          @include media-breakpoint-between(lg, lg) {
            margin-top: 2px;
          }

          @include media-breakpoint-down(sm) {
            margin-top: 2px;
          }
        }

        @include media-breakpoint-between(md, md) {
          @include large-checkbox;
        }

        @include media-breakpoint-up(xl) {
          @include large-checkbox;
        }
      }
    }

    .payment-type {
      color: var(--kui-gray-950);
      font-family: var(--kui-font-secondary);
      font-size: 14px;
      font-weight: 700;
      line-height: 20px;
      text-transform: uppercase;
      margin-bottom: 8px;
    }

    .simplepay {
      max-width: 300px;
      display: block;
      margin-top: 8px;

      img {
        width: 100%;
        height: auto;
      }
    }

    .mindmegette-form-label {
      font-size: 14px;
      line-height: 20px;
      text-transform: initial;
      letter-spacing: initial;
    }

    .mindmegette-form-textarea {
      margin-bottom: 8px;
      font-size: 12px;
      line-height: 16px;
      letter-spacing: 0.12px;
      min-height: 64px;
    }

    ::ng-deep .mindmegette-form-textarea + kesma-form-control-error .form-error {
      margin: 12px 0;
    }

    .purchase-checkboxes {
      margin-top: 12px;

      .mindmegette-form-checkbox {
        margin-bottom: 12px;
        color: var(--kui-gray-950);
        font-size: 14px;
        line-height: 20px;
      }

      ::ng-deep .mindmegette-form-checkbox + kesma-form-control-error .form-error {
        margin: 12px 0;
      }
    }
  }

  &-total {
    display: flex;
    justify-content: space-between;
    color: var(--kui-gray-950);
    font-family: var(--kui-font-secondary);
    font-size: 16px;
    font-weight: 700;
    line-height: 22px;
    letter-spacing: 0.16px;
    margin-bottom: 20px;
  }

  &-action {
    mindmegette-simple-button ::ng-deep button {
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

.mindmegette-form-general-error {
  margin-bottom: 32px;

  @include media-breakpoint-between(lg, lg) {
    margin-bottom: 24px;
  }

  @include media-breakpoint-down(sm) {
    margin-bottom: 24px;
  }

  &.mt-12 {
    margin-top: 12px;
  }

  kesma-icon {
    display: inline-block;
    margin-right: 8px;
    vertical-align: top;
  }
}
