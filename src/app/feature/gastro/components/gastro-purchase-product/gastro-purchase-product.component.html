<app-gastro-purchase-occasion [experience]="details().experience" [selectedOccasion]="selectedOccasion()" [withImage]="true"></app-gastro-purchase-occasion>

<form (ngSubmit)="navigateToNextStep()" [formGroup]="formGroup">
  <!-- Id<PERSON>pontok -->
  <div class="purchase-form-group mb-12">
    <h4 class="purchase-form-group-title">Választható időpontok</h4>
    @if ((availableOccasions()?.length ?? 0) === 0) {
      <div class="occasion-no-options">
        Erre az eseményre nincs választható időpont. <a [routerLink]="['/elmenyek/esemenyek']">Tovább az összes eseményhez</a>
      </div>
    } @else {
      <div class="occasion-form-options">
        @for (availableOccasion of availableOccasions(); track availableOccasion.occasion.id; let index = $index) {
          <div
            class="occasion-form-option"
            [class.active]="availableOccasion.occasion.id === formGroup.value[formFields.occasion]"
            (click)="selectOccasion(availableOccasion.occasion)"
          >
            <div class="occasion-form-option-index">{{ index + 1 }}</div>
            <div class="occasion-form-option-date">
              @if (availableOccasion.occasion?.startOfExperienceEvent; as startDate) {
                <div>
                  {{ availableOccasion.occasion?.startOfExperienceEvent | formatDate: 'hungarian' }}
                </div>
                <div>
                  {{ availableOccasion.occasion?.startOfExperienceEvent | gastroDuration: availableOccasion.occasion?.duration }}
                </div>
              } @else {
                <div>Hamarosan</div>
                <div>{{ availableOccasion.occasion.place }}</div>
              }
            </div>
          </div>
        }
      </div>
    }
    @if (isSubmitted() && !formGroup.value[formFields.occasion]) {
      <div class="mindmegette-form-general-error">
        <kesma-icon class="error-icon" name="icon-exclamation-mark-circle" [size]="16"></kesma-icon>
        Egy időpont kiválasztása kötelező
      </div>
    }
  </div>

  <!-- Mennyiség -->
  <div class="purchase-form-group">
    <h4 class="purchase-form-group-title">Résztvevők száma</h4>
    <div class="quantity-form-options">
      @for (quantity of details().experience.studyAmount; track quantity.studentNumber; let i = $index) {
        @if (i + 1 <= selectedOccasionNumberOfSeats()) {
          <div class="quantity-form-option" [class.active]="quantity.studentNumber === formGroup.value[formFields.quantity]" (click)="selectQuantity(quantity)">
            <div class="quantity-form-option-illustration">
              @for (i of createRange(quantity.studentNumber); track i) {
                <kesma-icon name="icon-gastro-user"></kesma-icon>
              }
            </div>
            <div class="quantity-form-option-quantity">{{ quantity.studentNumber }} Fő</div>
            <div class="quantity-form-option-price">
              {{ quantity.amount | thousandSeparator }}
              Ft
            </div>
          </div>
        }
      }
    </div>
    <div class="purchase-form-group-text">
      Négynél több fős, illetve csapatépítő programra történő jelentkezés esetén kérj egyedi ajánlatot a
      <a href="mailto:<EMAIL>">kapcsolat&#64;mindmegette.hu</a> e-mail-címen!
    </div>
    @if (isSubmitted() && !formGroup.value[formFields.quantity]) {
      <div class="mindmegette-form-general-error mt-12">
        <kesma-icon class="error-icon" name="icon-exclamation-mark-circle" [size]="16"></kesma-icon>
        A résztvevők számának kiválasztása kötelező
      </div>
    }
  </div>

  <!-- Ajándék -->
  <div class="purchase-form-group">
    <h4 class="purchase-form-group-title">Ajándékozz élményt!</h4>
    <div class="purchase-form-group-text mt-8">
      Az ajándékjegyet kinyomtatható formában is elküldjük arra az e-mail-címre, amelyen a jegyet és a számlát is megkapod.
    </div>
    <div class="mindmegette-form-row">
      <div class="purchase-gift-select">
        <div>
          <label class="mindmegette-form-checkbox" for="isGiftTrue">
            <input [formControlName]="formFields.isGift" [value]="true" id="isGiftTrue" name="isGift" type="radio" />
            <span>Ajándékba adom</span>
          </label>
        </div>
        <div>
          <label class="mindmegette-form-checkbox" for="isGiftFalse">
            <input [formControlName]="formFields.isGift" [value]="false" id="isGiftFalse" name="isGift" type="radio" />
            <span>Saját részre veszem</span>
          </label>
        </div>
      </div>
    </div>
    @if (isSubmitted() && formGroup.value[formFields.isGift] === null) {
      <div class="mindmegette-form-general-error mt-12">
        <kesma-icon class="error-icon" name="icon-exclamation-mark-circle" [size]="16"></kesma-icon>
        Egy lehetőség kiválasztása kötelező
      </div>
    }
  </div>

  <!-- Fizetési mód -->
  <div class="purchase-form-group">
    <h4 class="purchase-form-group-title">Fizetési mód</h4>
    <div class="payment-type">Bankkártya SimplePay</div>
    <div class="purchase-form-group-text">
      A SimplePay rendszere lehetővé teszi a kártyaelfogadást, biztonságos, SSL-protokollt használ. Bankkártyás fizetéskor átirányítunk a SimplePay
      fizetőoldalára.
    </div>
    <a class="simplepay" href="http://simplepartner.hu/PaymentService/Fizetesi_tajekoztato.pdf" target="_blank">
      <img alt="SimplePay vásárlói tájékoztató" src="/assets/images/simplepay-payment-methods.png" />
    </a>
  </div>

  <!-- Megjegyzés -->
  <div class="purchase-form-group">
    <!-- Kupon -->
    @if (isCouponEnabled) {
      <div class="purchase-form-group-text mb-12">Amennyiben rendelkezel kuponkóddal, azt a vásárlás utolsó lépésében tudod megadni.</div>
    }
    <div class="purchase-form-group-text">
      Ha további kérdésed van, hívd munkatársunkat a <a href="tel:**********">+36-1-999-4760</a> számon, vagy írj e-mailt a
      <a href="mailto:<EMAIL>">kapcsolat&#64;mindmegette.hu</a> címre!
    </div>
  </div>

  <!-- Nyilatkozatok -->
  <div class="purchase-form-group">
    <h4 class="purchase-form-group-title">Nyilatkozatok</h4>
    <div class="purchase-checkboxes">
      <kesma-form-control [withIcon]="true" class="checkbox">
        <label class="mindmegette-form-checkbox" for="terms">
          <input [formControlName]="formFields.terms" id="terms" type="checkbox" />
          <span
            >Megismertem és elfogadom a Mindmegette.hu weboldal
            <a class="mindmegette-form-checkbox-link" href="/felhasznalasi-feltetelek" target="_blank">felhasználási feltételeit</a>. A honlapra történő
            regisztrációval elfogadom a Mediaworks Hungary Zrt. (1082 Budapest, Üllői út 82.) Mindmegette.hu weboldallal kapcsolatos adatkezelésre vonatkozó
            <a class="mindmegette-form-checkbox-link" href="/adatvedelmi-tajekoztato" target="_blank">adatvédelmi tájékoztatóját</a> és hozzájárulok ahhoz, hogy
            az általam közölt adatokat a regisztrációval összefüggő célokból és az általam használt szolgáltatások működtetése érdekében a Mediaworks Hungary
            Zrt. kezelje.
          </span>
        </label>
      </kesma-form-control>
      <kesma-form-control [withIcon]="true" class="checkbox">
        <label class="mindmegette-form-checkbox" for="marketing">
          <input [formControlName]="formFields.marketing" id="marketing" type="checkbox" />
          <span>
            Érdekelnek a Mediaworks Hungary Zrt. és más harmadik felek különleges ajánlatai és reklámjai (ideértve például a recept és más tartalmi ajánlókat),
            ezért szeretnék direkt marketing - közvetlen üzletszerzési célú - hírleveleket kapni e-mailen. Hozzájárulok a megadott személyes adataim kezeléséhez
            az adatkezelési tájékoztatóban foglaltak szerint (a személyes adatokat csak a Mediaworks Hungary Zrt. fogja kezelni). Adatvédelmi tájékoztató:
            <a class="mindmegette-form-checkbox-link" href="/adatvedelmi-tajekoztato" target="_blank">link</a>
          </span>
        </label>
      </kesma-form-control>
    </div>
  </div>

  <!-- Végösszeg -->
  @if (totalAmount()) {
    <div class="purchase-total">
      <h4 class="purchase-total-title">Végösszeg:</h4>
      <div class="purchase-total-amount">
        {{ (totalAmount() | thousandSeparator) + ' Ft / ' + formGroup.value[formFields.quantity] + ' Fő' }}
      </div>
    </div>
  }

  @if (isSubmitted() && !formGroup.valid) {
    <div class="mindmegette-form-general-error">
      <kesma-icon class="error-icon" name="icon-exclamation-mark-circle" [size]="16"></kesma-icon>
      Kérjük tölts ki minden kötelező adatot a folytatáshoz!
    </div>
  }

  <div class="purchase-action">
    <mindmegette-simple-button
      [isSubmit]="true"
      [disabled]="(availableOccasions()?.length ?? 0) === 0"
      class="w-100"
      color="primary"
      icon="mindmegette-icon-arrow-right-no-color"
      iconPosition="right"
      [useKesmaIcon]="true"
      [kesmaIconSize]="24"
    >
      {{ user() ? 'Tovább a vásárláshoz' : 'A jelentkezéshez lépj be' }}
    </mindmegette-simple-button>
  </div>
</form>
