import { ChangeDetectionStrategy, Component, computed, effect, inject, input, InputSignal, Signal, signal, WritableSignal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import {
  backendDateToDate,
  IconComponent,
  KesmaFormControlComponent,
  markControlsTouched,
  PortalConfigSetting,
  ThousandSeparatorPipe,
  User,
} from '@trendency/kesma-ui';
import { DateFnsModule, FormatPipeModule } from 'ngx-date-fns';
import {
  BackendExperienceOccasionDetails,
  BackendExperienceOccasionDetailsOccasion,
  BackendExperienceOccasionPartialDetails,
  BackendExperienceStudyAmount,
} from '../../definitions/experience.definitions';
import { GastroDurationPipe } from '../../shared/gastro-duration.pipe';
import { GastroPurchaseService } from '../../shared/gastro-purchase.service';
import { GastroPurchaseProductStepData, GastroPurchaseSessionData, GastroPurchaseStepType } from '../../definitions/gastro-purchase.definitions';
import { ActivatedRoute, Params, Router, RouterLink } from '@angular/router';
import { toSignal } from '@angular/core/rxjs-interop';
import { map, startWith } from 'rxjs/operators';
import { GastroPurchaseOccasionComponent } from '../gastro-purchase-occasion/gastro-purchase-occasion.component';
import { AuthService, MindmegetteSimpleButtonComponent, PortalConfigService } from '../../../../shared';
import { FormatDatePipe } from '@trendency/kesma-core';

@Component({
  selector: 'app-gastro-purchase-product',
  imports: [
    CommonModule,
    FormatPipeModule,
    DateFnsModule,
    ReactiveFormsModule,
    GastroDurationPipe,
    GastroPurchaseOccasionComponent,
    RouterLink,
    MindmegetteSimpleButtonComponent,
    IconComponent,
    ThousandSeparatorPipe,
    KesmaFormControlComponent,
    FormatDatePipe,
  ],
  templateUrl: './gastro-purchase-product.component.html',
  styleUrl: './gastro-purchase-product.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class GastroPurchaseProductComponent {
  readonly #route: ActivatedRoute = inject(ActivatedRoute);
  readonly #authService: AuthService = inject(AuthService);
  readonly portalConfigService: PortalConfigService = inject(PortalConfigService);
  readonly #gastroPurchaseService: GastroPurchaseService = inject(GastroPurchaseService);
  readonly #router: Router = inject(Router);

  constructor() {
    effect(() => {
      if (this.availableOccasions().length === 1) {
        this.formGroup.controls['occasion'].setValue(this.availableOccasions()[0].occasion.id);
      }
    });
  }

  readonly sessionData: WritableSignal<GastroPurchaseSessionData> = this.#gastroPurchaseService.sessionData;
  readonly slug: Signal<string | undefined> = toSignal<string>(this.#route.params.pipe(map((params: Params) => params['slug'])));
  readonly selectedOccasionNumberOfSeats: WritableSignal<number> = signal(0);
  readonly details: InputSignal<BackendExperienceOccasionDetails> = input.required<BackendExperienceOccasionDetails, BackendExperienceOccasionDetails>({
    transform: (value: BackendExperienceOccasionDetails) => {
      this.selectedOccasionNumberOfSeats.set(value?.occasion?.remainingNumberOfSeats);
      return value;
    },
  });
  readonly availableOccasions: Signal<BackendExperienceOccasionPartialDetails[]> = computed(() =>
    [this.details() as BackendExperienceOccasionPartialDetails]
      .concat(this.details()?.nextOccasions)
      .filter((item) => +item.occasion.remainingNumberOfSeats > 0 && item.occasion.startOfExperienceEvent)
      .sort((a, b) => {
        const aDate: Date = backendDateToDate(a.occasion?.startOfExperienceEvent ?? '') ?? new Date();
        const bDate: Date = backendDateToDate(b.occasion?.startOfExperienceEvent ?? '') ?? new Date();
        return aDate.getTime() - bDate.getTime();
      })
  );
  readonly user: Signal<User | undefined> = toSignal(this.#authService.currentUserSubject);
  readonly isSubmitted: WritableSignal<boolean> = signal<boolean>(false);

  readonly isCouponEnabled: boolean = this.portalConfigService.isConfigSet(PortalConfigSetting.MENU_TYPE_ORDERS_COUPON);

  readonly maxTextLength: number = 300;

  readonly formFields = {
    occasion: 'occasion',
    quantity: 'quantity',
    isGift: 'isGift',
    orderComment: 'orderComment',
    terms: 'terms',
    marketing: 'marketing',
  } as const;

  readonly formGroup = new FormGroup({
    [this.formFields.occasion]: new FormControl<string | null>(this.sessionData().occasion ?? null, [Validators.required]),
    [this.formFields.quantity]: new FormControl<number | null>(this.sessionData().quantity ?? null, [Validators.required]),
    [this.formFields.isGift]: new FormControl<boolean | null>(this.sessionData().isGift ?? null, [Validators.required]),
    [this.formFields.orderComment]: new FormControl<string | null>(this.sessionData().orderComment ?? null, [Validators.maxLength(this.maxTextLength)]),
    [this.formFields.terms]: new FormControl<boolean>(false, [Validators.requiredTrue]),
    [this.formFields.marketing]: new FormControl<boolean>(false),
  });

  readonly occasionFormControlValue: Signal<string | null> = toSignal<string | null>(
    this.formGroup.controls[this.formFields.occasion].valueChanges.pipe(startWith(this.sessionData().occasion ?? null)),
    { requireSync: true }
  );
  readonly selectedOccasion: Signal<BackendExperienceOccasionPartialDetails> = computed(() => {
    const occasionByFormControl = this.availableOccasions()?.find((item) => item.occasion.id === this.occasionFormControlValue());
    // Form value needs to be reset silently (to unavailable occasion bugs if form populated from stored session)
    this.formGroup.controls[this.formFields.occasion].patchValue(occasionByFormControl?.occasion?.id ?? null, { emitEvent: false });
    return occasionByFormControl ?? this.details();
  });
  readonly quantityFormControlValue: Signal<number | null> = toSignal<number | null>(
    this.formGroup.controls[this.formFields.quantity].valueChanges.pipe(startWith(this.sessionData().quantity ?? null)),
    { requireSync: true }
  );
  readonly totalAmount: Signal<number> = computed(
    () => this.details()?.experience.studyAmount.find((item) => item.studentNumber === this.quantityFormControlValue())?.amount ?? 0
  );

  createRange(n: number): number[] {
    return Array.from({ length: n }, (_, i) => i + 1);
  }

  selectOccasion(occasion: BackendExperienceOccasionDetailsOccasion): void {
    this.selectedOccasionNumberOfSeats.set(occasion?.remainingNumberOfSeats);
    this.formGroup.controls[this.formFields.occasion].patchValue(occasion.id);
    if (occasion.remainingNumberOfSeats) {
      this.formGroup.controls[this.formFields.quantity].patchValue(1);
    }
  }

  selectQuantity(studyAmount: BackendExperienceStudyAmount): void {
    this.formGroup.controls[this.formFields.quantity].patchValue(studyAmount.studentNumber);
  }

  navigateToNextStep(): void {
    if (this.formGroup) {
      markControlsTouched(this.formGroup);
    }

    this.isSubmitted.set(true);

    if (!this.formGroup.valid) {
      if (this.user()) {
        return;
      }
      // If user is not logged in and form is invalid we save form data and after log in we will redirect to here
      this.#gastroPurchaseService.saveStepData(GastroPurchaseStepType.PRODUCT, {
        ...(this.formGroup.value as GastroPurchaseProductStepData),
        slug: this.selectedOccasion().occasion.slug ?? '',
      });
      this.#router.navigate(['/bejelentkezes'], {
        queryParams: { redirect: `/elmenyek/esemenyek/${this.selectedOccasion().occasion.slug ?? ''}` },
      });
      return;
    }

    this.#gastroPurchaseService.handleStep(GastroPurchaseStepType.PRODUCT, {
      ...(this.formGroup.value as GastroPurchaseProductStepData),
      slug: this.selectedOccasion().occasion.slug ?? '',
    });

    // If user is not logged in, we just save given data and display warning instead of next step
    if (!this.user()) {
      this.#router.navigate(['/bejelentkezes'], {
        queryParams: { redirect: `/elmenyek/vasarlas/${this.selectedOccasion().occasion.slug ?? ''}` },
      });
    } else {
      this.#router.navigate(['/elmenyek/vasarlas', this.selectedOccasion().occasion.slug ?? '']);
    }
  }
}
