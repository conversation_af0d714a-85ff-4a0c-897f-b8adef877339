<div class="purchase-section-title">A kiválasztott tanfolyam adatai:</div>
<app-gastro-purchase-occasion [experience]="details().experience" [selectedOccasion]="selectedOccasion()"></app-gastro-purchase-occasion>

<div class="purchase-section-title">Időpontja:</div>
<div class="purchase-section-content">
  @if (details().occasion?.startOfExperienceEvent; as startDate) {
    {{ startDate | formatDate: 'hungarian' }}
    {{ startDate | gastroDuration: details().occasion?.duration }}
  } @else {
    Hamarosan
  }
</div>

<div class="purchase-section-title">Résztvevők száma:</div>
<div class="purchase-section-content">
  {{ sessionData().quantity + ' Fő / ' + (totalAmount() | thousandSeparator) + ' Forint' }}
</div>

<div class="purchase-section-title">Tanfolyam részvétel:</div>
<div class="purchase-section-content">
  {{ sessionData().isGift ? 'Ajándék kártya' : 'Saj<PERSON><PERSON> r<PERSON>' }}
</div>

<div class="purchase-section-title">Fizetési mód:</div>
<div class="purchase-section-content">Online bankkártyás fizetés - SimplePay</div>

<div class="purchase-section-title">
  Számlázási adatok:
  <a (click)="backToPreviousStep()" class="subscription-summary-group-title-link">
    <span>Módosítom</span>
    <kesma-icon name="mindmegette-icon-arrow-right-no-color" [size]="20"></kesma-icon>
  </a>
</div>
<div class="purchase-section-content">
  <div>{{ sessionData().invoiceName }}</div>
  <div>{{ sessionData().invoiceZip }} {{ sessionData().invoiceCity }}, {{ sessionData().invoiceAddress }}</div>
  <div>{{ sessionData().taxNumber }}</div>
</div>

<div class="purchase-section-title">
  Telefonszám:
  <a (click)="backToPreviousStep()" class="subscription-summary-group-title-link">
    <span>Módosítom</span>
    <kesma-icon name="mindmegette-icon-arrow-right-no-color" [size]="20"></kesma-icon>
  </a>
</div>
<div class="purchase-section-content">{{ sessionData().phoneNumber }}</div>

<div class="purchase-section-title">E-mail cím:</div>
<div class="purchase-section-content">{{ user()?.email }}</div>

<!-- Kupon -->
@if (isCouponEnabled) {
  <div class="purchase-section-title">Kuponkód megadása:</div>
  <div class="purchase-section-content coupon-code">Ha rendelkezel kuponkóddal, add meg itt, hogy érvényesítsük a kedvezményt.</div>
  <div class="mindmegette-form-row" [formGroup]="formGroup">
    <div class="purchase-coupon-code">
      <input
        [formControlName]="formFields.couponCode"
        class="mindmegette-form-input"
        id="couponCode"
        placeholder=""
        type="text"
        [readonly]="!!sessionData().coupon"
      />
      <mindmegette-simple-button [color]="sessionData().coupon ? 'outline' : 'primary'" (click)="validateOrDeleteCoupon()" [disabled]="isCouponValidating()">
        {{ sessionData().coupon ? 'Törlés' : isCouponValidating() ? 'Kérjük várj...' : 'Ellenőrzés' }}</mindmegette-simple-button
      >
    </div>
    @if (sessionData().coupon; as coupon) {
      <div class="purchase-coupon-code-result success">
        <kesma-icon class="success-icon" name="icon-circle-checkmark" [size]="16"></kesma-icon>
        A kupon sikeresen aktiválva, a fizetéskor
        {{ coupon.amount ? (coupon.amount | thousandSeparator) + ' Ft fix összegű' : coupon.percent + '%' }} kedvezményt alkalmazunk a végösszegre.
      </div>
    } @else if (isCouponError()) {
      <div class="purchase-coupon-code-result error">
        <kesma-icon class="error-icon" name="icon-exclamation-mark-circle" [size]="16"></kesma-icon>
        Hibás vagy lejárt kupon, kérjük ellenőrizd a megadott kódot!
      </div>
    }
  </div>
}

<div class="purchase-total" [class.discounted]="discountedTotal()">
  <div class="purchase-total-title">Végösszeg:</div>
  <div class="purchase-total-amount">
    {{ (totalAmount() | thousandSeparator) + ' Ft / ' + sessionData().quantity + ' Fő' }}
  </div>
</div>

@if (discountedTotal()) {
  <div class="purchase-total">
    <h4 class="purchase-total-title">Kedvezményes végösszeg:</h4>
    <div class="purchase-total-amount">
      {{ (discountedTotal() | thousandSeparator) + ' Ft / ' + sessionData().quantity + ' Fő' }}
    </div>
  </div>
}

@if (!isLoading() && error()) {
  <div class="mindmegette-form-general-error">
    <kesma-icon class="error-icon" name="icon-exclamation-mark-circle" [size]="16"></kesma-icon>
    <span>{{ error() }}</span>
  </div>
}

<div class="purchase-action">
  <mindmegette-simple-button
    (click)="navigateToNextStep()"
    [disabled]="isLoading()"
    class="w-100"
    color="primary"
    icon="mindmegette-icon-arrow-right-no-color"
    iconPosition="right"
    [useKesmaIcon]="true"
    [kesmaIconSize]="24"
  >
    {{ isLoading() ? 'Kérjük várj...' : 'Tovább a fizetéshez' }}
  </mindmegette-simple-button>
</div>
