<section>
  <div class="wrapper with-aside">
    <div class="left-column occasion-column">
      <form [formGroup]="formGroup" (ngSubmit)="onExperienceSearch()" class="occasion-search-form">
        <div class="occasion-form-fields">
          <h1 class="search-text">Keresés</h1>
          <h2 class="search-count">Összes élmény ({{ (limitable$ | async)?.rowAllCount ?? 0 }}db)</h2>
          <kesma-form-control>
            <input class="mindmegette-form-input" formControlName="global_filter" type="text" placeholder="Írd be a keresőszót..." />
          </kesma-form-control>
          <mindmegette-form-select
            ariaLabel="Élmények"
            [clearable]="true"
            [formGroup]="formGroup"
            [searchable]="true"
            [options]="experiences$ | async"
            [isOccasionSearchPage]="true"
            bindLabel="title"
            bindValue="slug"
            controlName="experience_slug_filter"
            label="Élmény neve"
            placeholder="Válassz"
          >
          </mindmegette-form-select>
          <mindmegette-form-select
            ariaLabel="Tematikák"
            [clearable]="true"
            [formGroup]="formGroup"
            [searchable]="true"
            [options]="experienceCategories$ | async"
            [isOccasionSearchPage]="true"
            bindLabel="title"
            bindValue="id"
            controlName="category_ids[]"
            label="Tematika"
            placeholder="Válassz"
          >
          </mindmegette-form-select>
          @defer {
            <mindmegette-date-time-picker
              valueFormat="yyyy-MM-dd 00:00:00"
              displayFormat="yyyy. MM. dd."
              controlName="occasionDateFrom_filter"
              label="Kezdő dátum"
              placeholder="Válassz"
              [id]="'occasionDateFrom_filter'"
              [enableTime]="false"
              [formGroup]="formGroup"
              [isClearable]="true"
              [showDropdowns]="false"
              [showNowText]="false"
              [isOccasionSearchPage]="true"
            >
            </mindmegette-date-time-picker>
            <mindmegette-date-time-picker
              valueFormat="yyyy-MM-dd 23:59:59"
              displayFormat="yyyy. MM. dd."
              controlName="occasionDateTo_filter"
              label="Záró dátum"
              placeholder="Válassz"
              [id]="'occasionDateTo_filter'"
              [enableTime]="false"
              [formGroup]="formGroup"
              [isClearable]="true"
              [showDropdowns]="false"
              [showNowText]="false"
              [isOccasionSearchPage]="true"
            >
            </mindmegette-date-time-picker>
          }
          <mindmegette-form-select
            ariaLabel="Szabad férőhelyek"
            [clearable]="true"
            [formGroup]="formGroup"
            [searchable]="true"
            [options]="remainingNumberOfSeats"
            [isOccasionSearchPage]="true"
            controlName="remainingNumberOfSeats_filter"
            label="Szabad férőhelyek száma"
            bindLabel="label"
            bindValue="value"
            placeholder="Válassz"
          >
          </mindmegette-form-select>
          <mindmegette-form-select
            ariaLabel="Házigazdák"
            [clearable]="true"
            [formGroup]="formGroup"
            [searchable]="true"
            [options]="authors$ | async"
            [isOccasionSearchPage]="true"
            controlName="author_id_filter"
            bindValue="id"
            bindLabel="fullName"
            label="Házigazda"
            placeholder="Válassz"
          >
          </mindmegette-form-select>
        </div>
        <mindmegette-simple-button [isSubmit]="true" [wide]="true" color="primary"> Élmény keresése </mindmegette-simple-button>
      </form>
      <div class="occasion-list">
        @if (experienceOccasions$ | async; as occasions) {
          @for (occasion of occasions | keyvalue; track occasion.key; let first = $first) {
            <h2 class="occasion-date" [class.first]="first">
              {{ occasion.key === 'Hamarosan' ? occasion.key : formatDate(occasion.key) }}
            </h2>
            @for (data of occasion.value; track data.occasionId) {
              <app-gastro-experience-occasion-card [isOccasionListPage]="true" [occasion]="data"></app-gastro-experience-occasion-card>
            }
            @if (first) {
              <mindmegette-newsletter [styleID]="NewsletterType"></mindmegette-newsletter>
            }
          } @empty {
            <div class="empty">A keresett kifejezésre nincs találat!</div>
          }
        }
        @if (limitable$ | async; as limitable) {
          <mindmegette-pager
            *ngIf="limitable && limitable.pageMax! > 0"
            [hasFirstLastButton]="false"
            [hasSkipButton]="true"
            [isCountPager]="false"
            [isListPager]="true"
            [maxDisplayedPages]="5"
            [rowAllCount]="limitable.rowAllCount!"
            [rowOnPageCount]="limitable.rowOnPageCount!"
          >
          </mindmegette-pager>
        }
      </div>
    </div>
    <aside>
      @if (sidebar(); as sb) {
        <app-layout
          [layoutType]="LayoutPageType.SIDEBAR"
          [adPageType]="adPageType"
          [structure]="$any(sb.struct)"
          [configuration]="sb.content"
          [isExperienceOccasionList]="true"
          [isGastroPage]="true"
        ></app-layout>
      }
    </aside>
  </div>
</section>
