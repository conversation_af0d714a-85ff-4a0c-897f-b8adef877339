<section>
  <main class="wrapper">
    <div class="sides">
      <div class="left-side">
        <img
          [alt]="author.public_author_name"
          [src]="author.avatar?.bigUrl || author.avatar?.fullSizeUrl || '/assets/images/author-placeholder.svg'"
          class="author-avatar"
          loading="eager"
          fetchpriority="high"
        />
      </div>
      <div class="right-side">
        <div class="author-details">
          <div class="">
            <h1>{{ author.public_author_name }}</h1>
            <div *ngIf="author?.rank as rank" class="author-rank">{{ rank }}</div>
          </div>
          <ul class="social-icons">
            <li *ngIf="author?.facebook as facebook" class="social-icon">
              <a [href]="facebook" target="_blank" [attr.aria-label]="author.public_author_name + ' Facebook profilja'"
                ><i class="icon mindmegette-icon-facebook"></i
              ></a>
            </li>
            <li *ngIf="author?.pinterest as pinterest" class="social-icon">
              <a [href]="pinterest" target="_blank" [attr.aria-label]="author.public_author_name + ' Pinterest profilja'"
                ><i class="icon mindmegette-icon-pinterest"></i
              ></a>
            </li>
            <li *ngIf="author?.youtube as youtube" class="social-icon">
              <a [href]="youtube" target="_blank" [attr.aria-label]="author.public_author_name + ' Youtube profilja'"><i class="icon icon-youtube"></i></a>
            </li>
            <li *ngIf="author?.instagram as instagram" class="social-icon">
              <a [href]="instagram" target="_blank" [attr.aria-label]="author.public_author_name + ' Instagram profilja'"
                ><i class="icon icon-instagram"></i
              ></a>
            </li>
            <li *ngIf="author?.tiktok as tiktok" class="social-icon">
              <a [href]="tiktok" target="_blank" [attr.aria-label]="author.public_author_name + ' Tiktok profilja'"><i class="icon icon-tiktok"></i></a>
            </li>
          </ul>
        </div>
        <div *ngIf="author?.publicAuthorDescription as description" class="author-description">{{ description }}</div>
        <ng-container *ngIf="author?.favoriteFoods">
          <h2 class="author-title">Kedvenc fogás</h2>
          <div class="author-description">{{ author?.favoriteFoods }}</div>
        </ng-container>
        <ng-container *ngIf="author?.maestroTip">
          <h2 class="author-title">Maestro tipp!</h2>
          <div class="author-description">{{ author?.maestroTip }}</div>
        </ng-container>
      </div>
    </div>
    <app-gastro-author-experience-box [occasions]="authorExperiences"></app-gastro-author-experience-box>
    <div class="author-filter">
      <div class="author-filter-search">
        <div class="author-filter-search-input">
          <img alt="Nagyító" class="icon" src="/assets/images/icons/magnifier-icon.svg" />
          <input (keydown.enter)="onSearch()" [(ngModel)]="filters['global_filter']" class="mindmegette-form-input" placeholder="Keresés" type="text" />
        </div>
        <mindmegette-simple-button (click)="onSearch()" color="primary">Keresés</mindmegette-simple-button>
      </div>
      <div class="author-filter-select">
        <ng-select
          (change)="onSearch()"
          [(ngModel)]="filters['publishDate_order[]']"
          ariaLabel="Sorbarendezési beállítások"
          [clearable]="false"
          [items]="publishDateSorts"
          [searchable]="false"
          bindValue="value"
          class="mindmegette-form-select"
        >
        </ng-select>
      </div>
    </div>

    <h2 class="results-label">
      {{ author.public_author_name }} tartalmai
      <sup *ngIf="portalConfigService.isConfigSet(PortalConfigSetting.SHOW_NUMBER_OF_ARTICLES_BY_AUTHORS_ENABLED)">
        ({{ limitable?.rowAllCount || 0 }} db)
      </sup>
    </h2>

    <div *ngIf="(limitable?.rowAllCount ?? 0) === 0" class="no-result">Nem található tartalom</div>

    <ng-container *ngFor="let dateGroup of articlesByDate">
      <div class="author-page-date-group">{{ dateGroup.date }}</div>
      <div class="articles">
        <ng-container *ngFor="let article of dateGroup.articles | slice: 0 : 6">
          <mindmegette-recipe-card
            *ngIf="article?.contentType === LayoutElementContentType.RECIPE; else articleCard"
            [data]="$any(article)"
            [hasBackground]="true"
            [styleID]="MindmegetteRecipeCardType.TopImageLeftAlignedCard"
          >
          </mindmegette-recipe-card>
          <ng-template #articleCard>
            <mindmegette-article-card [data]="article" [hasBackground]="true" [styleID]="MindmegetteArticleCardType.TopImageLeftAlignedCard">
            </mindmegette-article-card>
          </ng-template>
        </ng-container>
      </div>

      <!-- AD -->
      <ng-container *ngIf="dateGroup.articles?.length && dateGroup.articles.length >= 4">
        <kesma-advertisement-adocean
          *ngIf="adverts?.desktop?.['roadblock_1'] as ad"
          [ad]="ad"
          [style]="{
            margin: 'var(--ad-margin)',
          }"
        ></kesma-advertisement-adocean>

        <kesma-advertisement-adocean
          *ngIf="adverts?.mobile?.['mobilrectangle_1'] as ad"
          [ad]="ad"
          [style]="{
            margin: 'var(--ad-margin)',
          }"
        ></kesma-advertisement-adocean>
      </ng-container>
      <!-- END AD -->

      <div *ngIf="dateGroup.articles?.length && dateGroup.articles.length > 6" class="articles">
        <ng-container *ngFor="let article of dateGroup.articles | slice: 6 : 12">
          <mindmegette-recipe-card
            *ngIf="article?.contentType === LayoutElementContentType.RECIPE; else articleCard"
            [data]="$any(article)"
            [hasBackground]="true"
            [styleID]="MindmegetteRecipeCardType.TopImageLeftAlignedCard"
          >
          </mindmegette-recipe-card>
          <ng-template #articleCard>
            <mindmegette-article-card [data]="article" [hasBackground]="true" [styleID]="MindmegetteArticleCardType.TopImageLeftAlignedCard">
            </mindmegette-article-card>
          </ng-template>
        </ng-container>
      </div>

      <!-- AD -->
      <ng-container *ngIf="dateGroup.articles?.length && dateGroup.articles.length >= 10">
        <kesma-advertisement-adocean
          *ngIf="adverts?.desktop?.['roadblock_2'] as ad"
          [ad]="ad"
          [style]="{
            margin: 'var(--ad-margin)',
          }"
        ></kesma-advertisement-adocean>

        <kesma-advertisement-adocean
          *ngIf="adverts?.mobile?.['mobilrectangle_2'] as ad"
          [ad]="ad"
          [style]="{
            margin: 'var(--ad-margin)',
          }"
        ></kesma-advertisement-adocean>
      </ng-container>
      <!-- END AD -->

      <div *ngIf="dateGroup.articles?.length && dateGroup.articles.length > 12" class="articles">
        <ng-container *ngFor="let article of dateGroup.articles | slice: 12">
          <mindmegette-recipe-card
            *ngIf="article?.contentType === LayoutElementContentType.RECIPE; else articleCard"
            [data]="$any(article)"
            [hasBackground]="true"
            [styleID]="MindmegetteRecipeCardType.TopImageLeftAlignedCard"
          >
          </mindmegette-recipe-card>
          <ng-template #articleCard>
            <mindmegette-article-card [data]="article" [hasBackground]="true" [styleID]="MindmegetteArticleCardType.TopImageLeftAlignedCard">
            </mindmegette-article-card>
          </ng-template>
        </ng-container>
      </div>
    </ng-container>

    <mindmegette-pager
      *ngIf="limitable && limitable.pageMax! > 0"
      [allowAutoScrollToTop]="true"
      [hasFirstLastButton]="false"
      [hasSkipButton]="true"
      [isCountPager]="false"
      [isListPager]="true"
      [maxDisplayedPages]="5"
      [rowAllCount]="limitable.rowAllCount!"
      [rowOnPageCount]="limitable.rowOnPageCount!"
    >
    </mindmegette-pager>
  </main>
</section>
