import { inject, Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { SeoService } from '@trendency/kesma-core';
import { ApiResponseMetaList, ApiResult, RedirectService as KesmaRedirectService } from '@trendency/kesma-ui';
import { ApiService, BackendAuthorData, RedirectService } from '../../../shared';

@Injectable()
export class AuthorListResolver {
  constructor(
    private readonly apiService: ApiService,
    private readonly router: Router,
    private readonly seoService: SeoService,
    private readonly redirectService: RedirectService
  ) {}
  private readonly kesmaRedirectService = inject(KesmaRedirectService);

  resolve(route: ActivatedRouteSnapshot): Observable<ApiResult<BackendAuthorData[], ApiResponseMetaList>> {
    // Redirect plural url to singular
    if (this.seoService.currentUrl.includes('szerzok')) {
      this.redirectService.redirectOldUrl(`szerzo`);
    }

    const currentPage = route.queryParams['page'] ? parseInt(route.queryParams['page'], 10) - 1 : 0;
    return this.apiService.getAuthors(currentPage, 20, true).pipe(
      map((res) => {
        if (this.kesmaRedirectService.shouldBeRedirect(currentPage, res.data)) {
          this.kesmaRedirectService.redirectOldUrl(`szerzo`, false, 302);
        }
        return res;
      }),
      catchError((err: Error) => {
        this.router
          .navigate(['/', '404'], {
            state: { errorResponse: JSON.stringify(err) },
            skipLocationChange: true,
          })
          .then();
        return throwError(() => err);
      })
    );
  }
}
