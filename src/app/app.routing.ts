import { Routes } from '@angular/router';
import { AuthGuard, BaseComponent, Error404Component, InitResolver } from './shared';
import { NewsletterSubscribeComponent } from '@feature/newsletter/newsletter-subscribe/newsletter-subscribe.component';
import { CheckRedirectBefore404Guard } from '@trendency/kesma-ui';

export const appRoutes: Routes = [
  {
    path: 'layout-editor',
    loadChildren: () => import('./feature/layout-editor/layout-editor.routing').then((m) => m.layoutEditorRoutes),
  },
  {
    path: '',
    component: BaseComponent,
    resolve: { data: InitResolver },
    children: [
      {
        path: '',
        pathMatch: 'full',
        loadChildren: () => import('./feature/home/<USER>').then((m) => m.homeRoutes),
      },
      // "short-circuit" - ha a file nem létezik, az SSR lefut - ami file lehet, azt küldjük 404-re
      {
        path: 'assets/:file',
        redirectTo: '404',
      },
      {
        path: 'assets/:dir/:file',
        redirectTo: '404',
      },
      {
        path: 'script/:file',
        redirectTo: '404',
      },
      {
        path: 'elmenyek',
        loadChildren: () => import('./feature/gastro/gastro.routes').then((m) => m.gastroRoutes),
      },
      {
        path: 'rovat/:categorySlug',
        loadChildren: () => import('./feature/category/category.routing').then((m) => m.categoryRoutes),
        data: {
          omitGlobalPageView: true,
        },
      },
      {
        path: 'recept',
        loadChildren: () => import('./feature/random-recipe/random-recipe.routing').then((m) => m.randomRecipeRouting),
      },
      {
        path: 'recept/:recipeSlug',
        loadChildren: () => import('./feature/recipe-page/recipe-page.routing').then((m) => m.recipePageRoutes),
        data: {
          skipSeoMetaCheck: true,
        },
      },
      {
        path: 'receptkategoria/:recipeCategorySlug',
        loadChildren: () => import('./feature/recipe-category/recipe-category.routing').then((m) => m.recipeCategoryRoutes),
      },
      {
        path: 'hozzavalok',
        loadChildren: () => import('./feature/ingredient-list/ingredient-list.routing').then((m) => m.ingredientListRouting),
      },
      {
        path: 'hozzavalo/:ingredientSlug',
        loadChildren: () => import('./feature/ingredient-page/ingredient-page.routing').then((m) => m.ingredientPageRouting),
      },
      {
        path: 'valogatas/:selectionSlug',
        loadChildren: () => import('./feature/selection-page/selection-page.routing').then((m) => m.selectionPageRouting),
      },
      {
        path: 'szerzo',
        loadChildren: () => import('./feature/authors/authors.routing').then((m) => m.AUTHOR_ROUTES),
      },
      {
        path: 'szerzok',
        loadChildren: () => import('./feature/authors/authors.routing').then((m) => m.AUTHOR_ROUTES),
      },
      {
        path: 'heti-menu',
        loadChildren: () => import('./feature/weekly-menu/weekly-menu.routing').then((m) => m.weeklyMenuRoutes),
      },
      {
        path: 'turpik',
        loadChildren: () => import('./feature/best-practices/best-practices.routing').then((m) => m.bestPracticesRouting),
        data: {
          isTapeHidden: true,
        },
      },
      {
        path: 'receptek-a-z',
        loadChildren: () => import('./feature/recipe-category-a-z/recipe-category-a-z.routing').then((m) => m.recipeCategoryAZRouting),
      },
      {
        path: 'legfrissebb-receptek',
        loadChildren: () => import('./feature/latest-recipes/latest-recipes-page.routing').then((m) => m.latestRecipesPageRouting),
      },
      {
        path: 'elrendezes-elonezet/:layoutHash',
        loadChildren: () => import('./feature/layout-preview/layout-preview.routing').then((m) => m.layoutPreviewRoutes),
      },
      {
        path: 'hozzavalo-elonezet/:previewHash',
        loadChildren: () => import('./feature/ingredient-page/ingredient-page.routing').then((m) => m.ingredientPageRouting),
      },
      {
        path: 'recept-elonezet/:previewHash',
        loadChildren: () => import('./feature/recipe-page/recipe-page.routing').then((m) => m.recipePageRoutes),
        data: {
          skipSeoMetaCheck: true,
        },
      },
      {
        path: 'heti-menu-elonezet/:previewHash',
        loadChildren: () => import('./feature/weekly-menu/weekly-menu.routing').then((m) => m.weeklyMenuRoutes),
      },
      {
        path: 'valogatas-elonezet/:previewHash',
        loadChildren: () => import('./feature/selection-page/selection-page.routing').then((m) => m.selectionPageRouting),
      },
      {
        path: 'cikk-elonezet/:previewHash',
        loadChildren: () => import('./feature/article-page/article-page.routing').then((m) => m.articlePageRoutes),
        data: {
          skipSeoMetaCheck: true,
          omitGlobalPageView: true,
        },
      },
      {
        path: 'cikk-elonezet/:previewHash/:previewType',
        loadChildren: () => import('./feature/article-page/article-page.routing').then((m) => m.articlePageRoutes),
        data: {
          skipSeoMetaCheck: true,
          omitGlobalPageView: true,
        },
      },
      {
        path: 'kereses',
        loadChildren: () => import('./feature/search-page/search-page.routing').then((m) => m.searchPageRouting),
      },
      {
        path: 'hirlevel-feliratkozas',
        data: {
          isFullWidth: true,
          isTapeHidden: true,
        },
        loadChildren: () => import('./feature/newsletter/newsletter.routing').then((m) => m.newsletterRoutes),
      },
      {
        path: 'panaszkezeles',
        loadChildren: () => import('./feature/complaint-handling/complaint-handling.routing').then((m) => m.complaintHandlingRoutes),
      },
      {
        path: 'hirlevel-feliratkozas-megerositese',
        data: { state: 'confirm', isFullWidth: true, isTapeHidden: true },
        component: NewsletterSubscribeComponent,
      },
      {
        path: 'hirlevel-feliratkozas-sikeres',
        data: { state: 'subscribe', isFullWidth: true, isTapeHidden: true },
        component: NewsletterSubscribeComponent,
      },
      {
        path: 'hirlevel-leiratkozas',
        data: { state: 'unsubscribe', isFullWidth: true, isTapeHidden: true },
        component: NewsletterSubscribeComponent,
      },
      {
        path: 'regisztracio',
        data: {
          isFullWidth: true,
          isTapeHidden: true,
        },
        loadChildren: () => import('./feature/registration/registration.routing').then((m) => m.registrationRouting),
      },
      {
        path: 'bejelentkezes',
        data: {
          isFullWidth: true,
          isTapeHidden: true,
        },
        loadChildren: () => import('./feature/login/login.routing').then((m) => m.loginRouting),
      },
      {
        path: 'elfelejtett-jelszo',
        data: {
          isFullWidth: true,
          isTapeHidden: true,
        },
        loadChildren: () => import('./feature/forgot-password/forgot-password.routing').then((m) => m.forgotPasswordRouting),
      },
      {
        path: 'kijelentkezes',
        data: {
          isFullWidth: true,
          isTapeHidden: true,
        },
        canActivate: [AuthGuard],
        loadChildren: () => import('./feature/logout/logout.routing').then((m) => m.logoutRouting),
      },
      {
        path: 'profil',
        data: {
          isFullWidth: true,
          isTapeHidden: true,
        },
        loadChildren: () => import('./feature/profile/profile.routing').then((m) => m.profileRoutes),
      },
      {
        path: 'receptbekuldes',
        data: {
          isFullWidth: true,
          isTapeHidden: true,
        },
        canActivate: [AuthGuard],
        loadChildren: () => import('./feature/recipe-submit/recipe-submit.routing').then((m) => m.recipeSubmitRouting),
      },
      {
        path: 'turpi-bekuldes',
        data: {
          isFullWidth: true,
          isTapeHidden: true,
        },
        canActivate: [AuthGuard],
        loadChildren: () => import('./feature/best-practice-submit/best-practice-submit.routing').then((m) => m.bestPracticeSubmitRouting),
      },
      {
        path: 'cimke',
        loadChildren: () => import('./feature/tags-page/tags-page.routing').then((m) => m.tagsPageRoutes),
      },
      {
        path: 'galeria/:gallerySlug',
        loadChildren: () => import('./feature/gallery-layer/gallery-layer.routing').then((m) => m.galleryLayerRoutes),
      },
      {
        path: 'gasztro-szotar',
        loadChildren: () => import('./feature/glossary/glossary.routes').then((m) => m.glossaryRoutes),
      },
      {
        path: 'gasztro-szotar-elonezet',
        loadChildren: () => import('./feature/glossary/glossary.routes').then((m) => m.glossaryRoutes),
        data: {
          skipSeoMetaCheck: true,
          omitGlobalPageView: true,
        },
      },
      {
        path: ':categorySlug/:articleSlug',
        loadChildren: () => import('./feature/article-page/article-page.routing').then((m) => m.articlePageRoutes),
        data: {
          skipSeoMetaCheck: true,
          omitGlobalPageView: true,
        },
      },
      {
        path: '404',
        data: {
          isTapeHidden: true,
        },
        component: Error404Component,
        canActivate: [CheckRedirectBefore404Guard],
      },
      {
        path: ':slug',
        loadChildren: () => import('./feature/static-page/static-page.routing').then((m) => m.staticPageRoutes),
      },
      {
        path: '**',
        redirectTo: '404',
      },
    ],
  },
];
