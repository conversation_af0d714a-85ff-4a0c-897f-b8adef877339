{"apps": [{"name": "mindmegette-dev", "script": "dist/server/server.mjs", "max_memory_restart": "500M", "max_restarts": 10, "env": {"PORT": 4000}}, {"name": "mindmegette-beta", "script": "dist/server/server.mjs", "max_memory_restart": "500M", "max_restarts": 10, "env": {"PORT": 4081}}, {"name": "mindmegette-test", "script": "dist/server/server.mjs", "exec_mode": "cluster", "instances": "1", "cwd": "/content/apps/mindmegettefe/app", "max_memory_restart": "500M", "max_restarts": 10, "env": {"PORT": 30114, "HTTPS_PROXY": "http://pkg-trendency:<EMAIL>:3128"}, "out_file": "/content/logs/mindmegettefe/out.log", "err_file": "/content/logs/mindmegettefe/err.log", "time": true, "merge_logs": true}, {"name": "mindmegette-prod", "script": "dist/server/server.mjs", "exec_mode": "cluster", "instances": "4", "cwd": "/content/apps/mindmegettefe/app", "max_memory_restart": "500M", "max_restarts": 10, "env": {"PORT": 30114, "HTTPS_PROXY": "http://trendency-prd:<EMAIL>:3128"}, "out_file": "/content/logs/mindmegettefe/out.log", "err_file": "/content/logs/mindmegettefe/err.log", "time": true, "merge_logs": true}]}